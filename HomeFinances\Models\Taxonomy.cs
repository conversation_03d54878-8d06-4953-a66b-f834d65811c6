﻿namespace HomeFinances.Models;

public class Taxonomy
{
    public Taxonomy()
    {
        Category = string.Empty;
        SubCategory = string.Empty;
        Tags = new List<string>();
        Confidence = 0; // You can update this default value as needed
        Amount = 0m;
        Details = String.Empty;
        ImageUrl = String.Empty;
    }

    public string Category { get; set; }
    public string SubCategory { get; set; }
    public string Details { get; set; }
    public List<string> Tags { get; set; }
    public string ImageUrl { get; set; }

    // Confidence value from 1 (lowest) to 100 (highest)
    public int Confidence { get; set; }

    public decimal Amount { get; set; } // Assuming you already have this property
}