﻿using System.Text.Json.Serialization;

namespace HomeFinances.Models.ChatGPT;

public class Content
{
    public Content()
    {
        Type = string.Empty;
        //Text = string.Empty;
        //ImageUrl = new ImageUrl();
    }

    [JsonPropertyName(name: "type")]
    public string Type { get; set; }

    [JsonPropertyName(name: "text")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public string Text { get; set; }

    [JsonPropertyName(name: "image_url")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public ImageUrl? ImageUrl { get; set; }
}