using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.Model;
using Moq;
using Xunit;
using HomeFinances.Repositories;
using CSharpFunctionalExtensions;

namespace CategoryRepositoryTests
{
    public class AwsDynamoCategoryRepositoryTests
    {
        private readonly Mock<IAmazonDynamoDB> _mockClient;
        private readonly AwsDynamoCategoryRepository _repository;
        private readonly Dictionary<string, List<string>> _store;

        public AwsDynamoCategoryRepositoryTests()
        {
            _store = new Dictionary<string, List<string>>(StringComparer.Ordinal);
            _mockClient = new Mock<IAmazonDynamoDB>();

            _mockClient
                .Setup(c => c.ScanAsync(It.IsAny<ScanRequest>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(() =>
                {
                    var response = new ScanResponse
                    {
                        Items = _store.Select(kvp => new Dictionary<string, AttributeValue>
                        {
                            ["Category"] = new AttributeValue { S = kvp.Key },
                            ["SubCategories"] = new AttributeValue { SS = kvp.Value }
                        }).ToList()
                    };
                    return response;
                });

            _mockClient
                .Setup(c => c.GetItemAsync(It.IsAny<GetItemRequest>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync((GetItemRequest req, CancellationToken _) =>
                {
                    var category = req.Key["Category"].S;
                    if (!_store.ContainsKey(category))
                    {
                        return new GetItemResponse { Item = new Dictionary<string, AttributeValue>() };
                    }
                    return new GetItemResponse
                    {
                        Item = new Dictionary<string, AttributeValue>
                        {
                            ["Category"] = new AttributeValue { S = category },
                            ["SubCategories"] = new AttributeValue { SS = _store[category] }
                        }
                    };
                });

            _mockClient
                .Setup(c => c.UpdateItemAsync(It.IsAny<UpdateItemRequest>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync((UpdateItemRequest req, CancellationToken _) =>
                {
                    var category = req.Key["Category"].S;
                    if (!_store.ContainsKey(category))
                    {
                        _store[category] = new List<string>();
                    }
                    if (req.AttributeUpdates.TryGetValue("SubCategories", out var update))
                    {
                        foreach (var val in update.Value.SS)
                        {
                            if (update.Action == AttributeAction.ADD)
                            {
                                if (!_store[category].Contains(val))
                                    _store[category].Add(val);
                            }
                            else if (update.Action == AttributeAction.DELETE)
                            {
                                _store[category].Remove(val);
                            }
                        }
                    }
                    return new UpdateItemResponse();
                });

            _repository = new AwsDynamoCategoryRepository(_mockClient.Object);
        }

        [Fact]
        public async Task<Result> AddSubCategoryAsync_NewCategory_AddsEntry()
        {
            var result = await _repository.AddSubCategoryAsync("Food", "Groceries");
            Assert.True(result.IsSuccess);
            Assert.True(_store.ContainsKey("Food"));
            Assert.Contains("Groceries", _store["Food"]);
        }

        [Fact]
        public async Task<Result> AddSubCategoryAsync_ExistingCategory_AddsAdditional()
        {
            _store["Food"] = new List<string> { "Groceries" };
            var result = await _repository.AddSubCategoryAsync("Food", "Restaurants");
            Assert.True(result.IsSuccess);
            var subs = _store["Food"];
            Assert.Equal(2, subs.Count);
            Assert.Contains("Groceries", subs);
            Assert.Contains("Restaurants", subs);
        }

        [Fact]
        public async Task<Result<bool>> RemoveSubCategoryAsync_ExistingSubCategory_RemovesIt()
        {
            _store["Food"] = new List<string> { "Groceries" };
            var result = await _repository.RemoveSubCategoryAsync("Food", "Groceries");
            Assert.True(result.IsSuccess);
            Assert.Empty(_store["Food"]);
            return result;
        }

        [Fact]
        public async Task<Result<bool>> RemoveSubCategoryAsync_NonExistingSubCategory_ReturnsFalse()
        {
            _store["Food"] = new List<string> { "Groceries" };
            var result = await _repository.RemoveSubCategoryAsync("Food", "Nonexistent");
            Assert.False(result.IsSuccess);
            return result;
        }

        [Fact]
        public async Task<Result<IList<string>>> GetSubCategoriesAsync_ExistingCategory_ReturnsList()
        {
            _store["Food"] = new List<string> { "Groceries", "Restaurants" };
            var result = await _repository.GetSubCategoriesAsync("Food");
            Assert.True(result.IsSuccess);
            var subs = result.Value;
            Assert.Equal(2, subs.Count);
            Assert.Contains("Groceries", subs);
            Assert.Contains("Restaurants", subs);
            return result;
        }

        [Fact]
        public async Task<Result<IList<string>>> GetSubCategoriesAsync_NonExistingCategory_ReturnsEmpty()
        {
            var result = await _repository.GetSubCategoriesAsync("NonExist");
            Assert.True(result.IsSuccess);
            Assert.Empty(result.Value);
            return result;
        }

        [Fact]
        public async Task<Result<IList<string>>> GetAllCategoriesAsync_ReturnsAllEntries()
        {
            _store["Food"] = new List<string> { "Groceries" };
            _store["Bills"] = new List<string> { "Electricity", "Water" };
            var result = await _repository.GetAllCategoriesAsync();
            Assert.True(result.IsSuccess);
            var all = result.Value;
            Assert.Equal(2, all.Count);
            Assert.True(all.Contains("Food"));
            Assert.True(all.Contains("Bills"));
            return result;
        }
    }
}