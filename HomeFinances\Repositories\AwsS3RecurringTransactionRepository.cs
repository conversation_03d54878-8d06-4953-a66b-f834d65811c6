using System.Net;
using System.Text;
using System.Text.Json;
using Amazon.S3;
using Amazon.S3.Model;

namespace HomeFinances.Repositories;

/// <summary>
///     AWS S3-based implementation of IRecurringTransactionRepository using JSON files stored in S3.
/// </summary>
public class AwsS3RecurringTransactionRepository : IRecurringTransactionRepository
{
    private const string RecurringTransactionsFileKey = "recurring-transactions";
    private const string S3FileName = "recurring-transactions.json";
    private readonly string _bucketName;
    private readonly ILogger<AwsS3RecurringTransactionRepository> _logger;
    private readonly IAmazonS3 _s3Client;

    public AwsS3RecurringTransactionRepository(IAmazonS3 s3Client
        , ILogger<AwsS3RecurringTransactionRepository> logger)
    {
        _s3Client = s3Client ?? throw new ArgumentNullException(paramName: nameof(s3Client)
                                                                , message: "S3 client cannot be null. AWS backend is required.");
        _logger = logger ?? throw new ArgumentNullException(paramName: nameof(logger));

        // Get bucket name from environment variable
        var connectionJson = Environment.GetEnvironmentVariable(variable: "AWS_S3_HOMEFINANCE_CONNECTION");
        if (!string.IsNullOrEmpty(value: connectionJson))
            try
            {
                var connectionSettings = JsonSerializer.Deserialize<Dictionary<string, string>>(json: connectionJson);
                if (connectionSettings != null && connectionSettings.TryGetValue(key: "BucketName"
                                                                                 , value: out var bucketName))
                    _bucketName = bucketName;
            }
            catch (Exception ex)
            {
                _logger.LogError(exception: ex
                                 , message: "Failed to parse AWS_S3_HOMEFINANCE_CONNECTION JSON");
                throw new InvalidOperationException(message: "Failed to parse AWS connection settings"
                                                    , innerException: ex);
            }

        if (string.IsNullOrEmpty(value: _bucketName))
        {
            _bucketName = "home-finances";
            _logger.LogWarning(message: "Using default bucket name: {BucketName}"
                               , _bucketName);
        }

        _logger.LogInformation(message: "Initialized AwsS3RecurringTransactionRepository with bucket: {BucketName}"
                               , _bucketName);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<RecurringTransaction>> GetAllAsync()
    {
        try
        {
            await VerifyBucketExistsAsync().ConfigureAwait(continueOnCapturedContext: false);

            var s3Key = GetS3Key();

            try
            {
                var request = new GetObjectRequest
                {
                    BucketName = _bucketName,
                    Key = s3Key
                };

                var response = await _s3Client.GetObjectAsync(request: request).ConfigureAwait(continueOnCapturedContext: false);

                using var reader = new StreamReader(stream: response.ResponseStream);
                var jsonContent = await reader.ReadToEndAsync().ConfigureAwait(continueOnCapturedContext: false);

                if (string.IsNullOrWhiteSpace(value: jsonContent))
                {
                    _logger.LogInformation(message: "Empty recurring transactions file found in S3");
                    return new List<RecurringTransaction>();
                }

                var transactions = JsonSerializer.Deserialize<List<RecurringTransaction>>(json: jsonContent);

                _logger.LogInformation(
                    message: "Successfully loaded {Count} recurring transactions from S3 bucket {BucketName}, key {Key}"
                    ,
                    transactions?.Count ?? 0
                    , _bucketName
                    , s3Key);

                return transactions ?? new List<RecurringTransaction>();
            }
            catch (AmazonS3Exception s3Ex) when (s3Ex.StatusCode == HttpStatusCode.NotFound)
            {
                _logger.LogInformation(
                    message: "Recurring transactions file not found in S3 bucket {BucketName}, key {Key}. Returning empty list."
                    ,
                    _bucketName
                    , s3Key);

                return new List<RecurringTransaction>();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(exception: ex
                             , message: "Error reading recurring transactions from S3");
            throw new InvalidOperationException(
                message: "Failed to read recurring transactions from AWS S3. AWS backend is required."
                , innerException: ex);
        }
    }

    public async Task<RecurringTransaction> GetByIdAsync(Guid id)
    {
        var allTransactions = await GetAllAsync().ConfigureAwait(continueOnCapturedContext: false);
        var transaction = allTransactions.FirstOrDefault(predicate: t => t.Id == id);

        if (transaction == null)
        {
            _logger.LogWarning(message: "Recurring transaction with ID {TransactionId} not found"
                               , id);
            throw new InvalidOperationException(message: $"Recurring transaction with ID {id} not found");
        }

        return transaction;
    }

    /// <inheritdoc />
    public async Task AddOrUpdateAsync(RecurringTransaction transaction)
    {
        if (transaction == null)
            throw new ArgumentNullException(paramName: nameof(transaction));

        try
        {
            var allTransactions = (await GetAllAsync().ConfigureAwait(continueOnCapturedContext: false)).ToList();

            // Remove existing transaction with same ID if it exists
            var existingIndex = allTransactions.FindIndex(match: t => t.Id == transaction.Id);
            if (existingIndex >= 0)
            {
                allTransactions[index: existingIndex] = transaction;
                _logger.LogInformation(message: "Updated existing recurring transaction with ID {TransactionId}"
                                       , transaction.Id);
            }
            else
            {
                allTransactions.Add(item: transaction);
                _logger.LogInformation(message: "Added new recurring transaction with ID {TransactionId}"
                                       , transaction.Id);
            }

            await WriteAllAsync(transactions: allTransactions).ConfigureAwait(continueOnCapturedContext: false);
        }
        catch (Exception ex)
        {
            _logger.LogError(exception: ex
                             , message: "Error adding/updating recurring transaction with ID {TransactionId}"
                             , transaction.Id);
            throw new InvalidOperationException(
                message: $"Failed to add/update recurring transaction with ID {transaction.Id}. AWS backend is required."
                , innerException: ex);
        }
    }

    /// <inheritdoc />
    public async Task DeleteAsync(Guid id)
    {
        try
        {
            var allTransactions = (await GetAllAsync().ConfigureAwait(continueOnCapturedContext: false)).ToList();

            var existingIndex = allTransactions.FindIndex(match: t => t.Id == id);
            if (existingIndex >= 0)
            {
                allTransactions.RemoveAt(index: existingIndex);
                await WriteAllAsync(transactions: allTransactions).ConfigureAwait(continueOnCapturedContext: false);
                _logger.LogInformation(message: "Deleted recurring transaction with ID {TransactionId}"
                                       , id);
            }
            else
            {
                _logger.LogWarning(message: "Attempted to delete non-existent recurring transaction with ID {TransactionId}"
                                   , id);
                throw new InvalidOperationException(message: $"Recurring transaction with ID {id} not found");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(exception: ex
                             , message: "Error deleting recurring transaction with ID {TransactionId}"
                             , id);
            throw new InvalidOperationException(
                message: $"Failed to delete recurring transaction with ID {id}. AWS backend is required."
                , innerException: ex);
        }
    }

    /// <inheritdoc />
    public async Task SaveAllAsync(List<RecurringTransaction> transactions)
    {
        if (transactions == null) throw new ArgumentNullException(paramName: nameof(transactions));

        await WriteAllAsync(transactions: transactions).ConfigureAwait(continueOnCapturedContext: false);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<RecurringTransaction>> SaveListAsync(List<RecurringTransaction> transactions)
    {
        if (transactions == null) throw new ArgumentNullException(paramName: nameof(transactions));

        _logger.LogInformation(
            message: "Attempting to write {Count} recurring transactions to S3 bucket: {BucketName}, Key: {FileKey}"
            , transactions.Count
            , _bucketName
            , RecurringTransactionsFileKey);
        try
        {
            var jsonContent = JsonSerializer.Serialize(value: transactions
                                                       , options: new JsonSerializerOptions { WriteIndented = true });
            var putObjectRequest = new PutObjectRequest
            {
                BucketName = _bucketName,
                Key = RecurringTransactionsFileKey,
                ContentBody = jsonContent
                                       ,
                ContentType = "application/json"
            };
            await _s3Client.PutObjectAsync(request: putObjectRequest);
            _logger.LogInformation(message: "Successfully wrote {Count} recurring transactions to S3."
                                   , transactions.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(exception: ex
                             , message: "Error writing recurring transactions to S3. Bucket: {BucketName}, Key: {FileKey}"
                             , _bucketName
                             , RecurringTransactionsFileKey);
            throw;
        }

        return transactions;
    }

    /// <summary>
    ///     Verifies that the S3 bucket exists and is accessible
    /// </summary>
    private async Task<bool> VerifyBucketExistsAsync()
    {
        try
        {
            var response = await _s3Client.ListBucketsAsync().ConfigureAwait(continueOnCapturedContext: false);
            var bucketExists = response.Buckets.Any(predicate: b => b.BucketName == _bucketName);

            if (!bucketExists)
            {
                _logger.LogWarning(message: "S3 bucket {BucketName} does not exist"
                                   , _bucketName);
                throw new InvalidOperationException(
                    message: $"S3 bucket {_bucketName} does not exist. Please create it before using this repository.");
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(exception: ex
                             , message: "Error verifying S3 bucket {BucketName}"
                             , _bucketName);
            throw new InvalidOperationException(message: $"Error accessing S3 bucket {_bucketName}. AWS backend is required."
                                                , innerException: ex);
        }
    }

    /// <summary>
    ///     Gets the S3 key for the recurring transactions file
    /// </summary>
    private string GetS3Key()
    {
        return $"{RecurringTransactionsFileKey}/{S3FileName}";
    }

    /// <summary>
    ///     Writes all recurring transactions to S3
    /// </summary>
    private async Task WriteAllAsync(IEnumerable<RecurringTransaction> transactions)
    {
        try
        {
            await VerifyBucketExistsAsync().ConfigureAwait(continueOnCapturedContext: false);

            var s3Key = GetS3Key();
            var jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            var jsonContent = JsonSerializer.Serialize(value: transactions
                                                       , options: jsonOptions);

            using var memoryStream = new MemoryStream(buffer: Encoding.UTF8.GetBytes(s: jsonContent));

            var putRequest = new PutObjectRequest
            {
                BucketName = _bucketName,
                Key = s3Key,
                InputStream = memoryStream
                                 ,
                ContentType = "application/json"
            };

            await _s3Client.PutObjectAsync(request: putRequest).ConfigureAwait(continueOnCapturedContext: false);

            _logger.LogInformation(
                message: "Successfully wrote {Count} recurring transactions to S3 bucket {BucketName}, key {Key}"
                ,
                transactions.Count()
                , _bucketName
                , s3Key);
        }
        catch (Exception ex)
        {
            _logger.LogError(exception: ex
                             , message: "Error writing recurring transactions to S3");
            throw new InvalidOperationException(
                message: "Failed to write recurring transactions to AWS S3. AWS backend is required."
                , innerException: ex);
        }
    }
}