﻿using Fluxor;
using HomeFinances.Models;
using static HomeFinances.Store.RecurringTransactions.RecurringTransactionActions;

namespace HomeFinances.Store.RecurringTransactions
{
    public class RecurringTransactionReducers
    {
        [ReducerMethod]
        public static RecurringTransactionsState ReduceLoadRecurringTransactionsAction(RecurringTransactionsState state, RecurringTransactionActions.LoadRecurringTransactionsAction action)
            => state with { Loading = new LoadingState { Status = LoadingStatusEnum.Loading} };

        [ReducerMethod]
        public static RecurringTransactionsState ReduceLoadRecurringTransactionsSuccessAction(RecurringTransactionsState state, RecurringTransactionActions.LoadRecurringTransactionsSuccessAction action)
            => state with { RecurringTransactions = action.Transactions, Loading = new LoadingState { Status = LoadingStatusEnum.Loaded } };

        [ReducerMethod]
        public static RecurringTransactionsState ReduceLoadRecurringTransactionsFailedAction(RecurringTransactionsState state, RecurringTransactionActions.LoadRecurringTransactionsFailedAction action)
            => state with { Loading = new LoadingState { Status = LoadingStatusEnum.Error } };
        [ReducerMethod]
        public static RecurringTransactionsState ReduceAddRecurringTransactionAction(RecurringTransactionsState state, AddOrUpdateRecurringTransactionAction action)
        {
            return state with { Loading = new LoadingState { Status = LoadingStatusEnum.Loading } };
        }

        [ReducerMethod]
        public static RecurringTransactionsState ReduceAddRecurringTransactionSuccessAction(RecurringTransactionsState state, AddOrUpdateRecurringTransactionSuccessAction action)
        {
            var existingTransaction = state.RecurringTransactions.FirstOrDefault(t => t.Id == action.Transaction.Id);
            List<RecurringTransaction> updatedTransactions;

            if (existingTransaction != null)
            {
                updatedTransactions = state.RecurringTransactions.Select(t => t.Id == existingTransaction.Id ? action.Transaction : t).ToList();
            }
            else
            {
                updatedTransactions = state.RecurringTransactions.Append(action.Transaction).ToList();
            }

            return state with { RecurringTransactions = updatedTransactions, Loading = new LoadingState { Status = LoadingStatusEnum.Loaded } };
        }

        [ReducerMethod]
        public static RecurringTransactionsState ReduceAddRecurringTransactionFailedAction(RecurringTransactionsState state, AddOrUpdateRecurringTransactionFailedAction action)
        {
            return state with { Loading = new LoadingState { Status = LoadingStatusEnum.Error, ErrorMessage = action.ErrorMessage } };
        }

        [ReducerMethod]
        public static RecurringTransactionsState ReduceDeleteRecurringTransactionAction(RecurringTransactionsState state, DeleteRecurringTransactionAction action)
        {
            return state with { Loading = new LoadingState { Status = LoadingStatusEnum.Loading } };
        }

        [ReducerMethod]
        public static RecurringTransactionsState ReduceDeleteRecurringTransactionSuccessAction(RecurringTransactionsState state, DeleteRecurringTransactionSuccessAction action)
        {
            var updatedTransactions = state.RecurringTransactions.Where(t => t.Id != action.TransactionId).ToList();
            return state with { RecurringTransactions = updatedTransactions, Loading = new LoadingState { Status = LoadingStatusEnum.Loaded } };
        }

        [ReducerMethod]
        public static RecurringTransactionsState ReduceDeleteRecurringTransactionFailedAction(RecurringTransactionsState state, DeleteRecurringTransactionFailedAction action)
        {
            return state with { Loading = new LoadingState { Status = LoadingStatusEnum.Error, ErrorMessage = action.ErrorMessage } };
        }
    }
}
