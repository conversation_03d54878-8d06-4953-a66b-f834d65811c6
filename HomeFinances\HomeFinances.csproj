﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="Components\FileUploadButton.razor" />
  </ItemGroup>


  <ItemGroup>
    <PackageReference Include="AWSSDK.S3" Version="4.0.0.5" />
    <PackageReference Include="Azure.Data.Tables" Version="12.8.3" />
    <PackageReference Include="Azure.Identity" Version="1.11.4" />
    <PackageReference Include="ChartJs.Blazor" Version="1.1.0" />
    <PackageReference Include="ChartJs.Blazor.Fork" Version="2.0.2" />
    <PackageReference Include="CSharpFunctionalExtensions" Version="3.6.0" />
    <PackageReference Include="CsvHelper" Version="32.0.3" />
    <PackageReference Include="Fluxor" Version="5.9.1" />
    <PackageReference Include="Fluxor.Blazor.Web" Version="5.9.1" />
    <PackageReference Include="Fluxor.Blazor.Web.ReduxDevTools" Version="5.9.1" />
    <PackageReference Include="MudBlazor" Version="6.20.0" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.1" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
        <PackageReference Include="WindowsAzure.Storage" Version="9.3.3" />
        <!-- AWS SDK for DynamoDB repository implementation -->
        <PackageReference Include="AWSSDK.DynamoDBv2" Version="4.0.0.5" />
  </ItemGroup>


  <ItemGroup>
    <None Update="Data\FutureTransactions.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Data\Uploads\BeehiveTransactions\O39241_100-4-18-2.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Data\Uploads\BeehiveTransactions\O39241_100-4-18.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Data\Uploads\BeehiveTransactions\O39241_100-4-19-2024.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Data\Uploads\BeehiveTransactions\O39241_100-4-22-2024.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Data\Uploads\BeehiveTransactions\O39241_100-4-24-2024.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Data\Uploads\BeehiveTransactions\O39241_100-4-28-2024.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Data\Uploads\BeehiveTransactions\O39241_100-5-1.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Data\Uploads\BeehiveTransactions\O39241_100-5-6-2024.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Data\Uploads\BeehiveTransactions\O39241_100-5-7-2024.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Data\Uploads\BeehiveTransactions\O39241_100-5-9-2023.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>


  <ItemGroup>
    <None Include="Components\Pages\FileUploadButton.razor" />
  </ItemGroup>


  <ItemGroup>
    <Folder Include="logs\" />
  </ItemGroup>
  <!-- Exclude CategoryRepositoryTests and Scripts from main project compile -->
  <ItemGroup>
    <Compile Remove="Repositories/CategoryRepositoryTests/**/*.cs" />
    <Compile Remove="Scripts/**/*.cs" />
  </ItemGroup>
  <!-- All logic has been consolidated back into the main HomeFinances project -->

</Project>
