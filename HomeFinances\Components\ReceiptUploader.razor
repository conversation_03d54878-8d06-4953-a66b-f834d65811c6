@using Microsoft.AspNetCore.Components.Forms

<MudFileUpload T="IBrowserFile" 
               Accept=".jpg,.jpeg,.png"
               MaximumFileCount="1"
               FilesChanged="@UploadFile"
               Disabled="@isProcessing">
    <ButtonTemplate>
        <MudButton HtmlTag="label"
                   Variant="Variant.Filled"
                   Color="Color.Primary"
                   StartIcon="@Icons.Material.Filled.CloudUpload"
                   for="@context.Id"
                   Disabled="@isProcessing">
            Upload Receipt
        </MudButton>
    </ButtonTemplate>
</MudFileUpload>

@if (!string.IsNullOrEmpty(uploadMessage))
{
    <MudAlert Severity="@(isSuccess ? Severity.Success : Severity.Error)"
             Color="@(isSuccess ? Color.Success : Color.Error)">
        @uploadMessage
    </MudAlert>
}

@if (isProcessing)
{
    <MudOverlay Visible="true">
        <MudProgressCircular Color="Color.Primary" 
                            Indeterminate="true" 
                            Size="Size.Large" />
        <MudText Class="mt-4" Style="color: white;">Processing receipt...</MudText>
    </MudOverlay>
} 