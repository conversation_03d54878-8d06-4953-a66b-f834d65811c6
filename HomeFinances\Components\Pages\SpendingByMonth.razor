﻿@page "/spending-by-month"
@rendermode InteractiveServer

<MudContainer>
    @foreach (var monthGroup in transactionsByMonth.OrderByDescending(kvp => kvp.Key))
    {
        
            var totalIncome = CalculateTotal(monthGroup.Value, true);
            var totalOutgo = CalculateTotal(monthGroup.Value, false);
            var net = totalIncome + totalOutgo; // Outgo values are negative
            var formattedMonthText = $"{new DateTime(monthGroup.Key.Year, monthGroup.Key.Month, 1):MMMM yyyy}        |          In: {totalIncome.ToString("C")} Out: {totalOutgo.ToString("C")}                Net: {net.ToString("C")}";
        
        <MudExpansionPanel Text="@formattedMonthText">

            @{ // Categories within each month
                var transactionsByCategory = GroupTransactionsByCategory(monthGroup.Value);
                foreach (var categoryGroup in transactionsByCategory)
                {
                    var categoryText = $"{categoryGroup.Key} {categoryGroup.Value.Sum(t => t.TransactionAmount).ToString("C")}";
                    <MudExpansionPanel Text="@categoryText">
                        @{
                            // Group by SubCategory within each category
                            var transactionsBySubCategory = categoryGroup.Value
                            .GroupBy(t => t.Taxonomy.SubCategory)
                            .ToDictionary(g => g.Key, g => g.ToList());

                            foreach (var subCategoryGroup in transactionsBySubCategory)
                            {
                                var subCategoryText = $"{subCategoryGroup.Key} Total: {subCategoryGroup.Value.Sum(t => t.TransactionAmount).ToString("C")}";
                                <div><strong>@subCategoryText</strong></div>
                                foreach (var transaction in subCategoryGroup.Value)
                                {
                                    <div style="margin-left: 20px;">@transaction.TransactionDate?.ToShortDateString() - @transaction.Description - @transaction.TransactionAmount.ToString("C")</div>
                                }
                            }
                        }
                        <MudText Typo="Typo.h6">Total: @categoryGroup.Value.Sum(t => t.TransactionAmount).ToString("C")</MudText>
                    </MudExpansionPanel>
                }
            }
        </MudExpansionPanel>
    }
</MudContainer>