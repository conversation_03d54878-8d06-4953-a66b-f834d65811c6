using Fluxor;
using HomeFinances.Components;
using HomeFinances.Services;
using MudBlazor.Services;

var builder = WebApplication.CreateBuilder(args: args);
// Configure logging
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Logging.AddDebug();
builder.Logging.SetMinimumLevel(level: LogLevel.Information);

// Add logging to verify Fluxor initialization and effect registration
builder.Logging.AddFilter(category: "Fluxor"
                          , level: LogLevel.Trace);

// Add services to the container.
builder.Services.AddRazorComponents()
   .AddInteractiveServerComponents();

// Ensure Fluxor is configured for Blazor and scans the correct assemblies
builder.Services.AddFluxor(configure: options =>
{
    // Scan the current assembly for states, reducers, and effects
    options.ScanAssemblies(assemblyToScan: typeof(Program).Assembly);

    // Enable Redux DevTools for debugging in development
    if (builder.Environment.IsDevelopment())
    {
        try
        {
            options.UseReduxDevTools(rdt =>
            {
                rdt.Name = "HomeFinances Store";
                rdt.EnableStackTrace();
                rdt.MaximumHistoryLength = 50; // Limit history to prevent memory issues
            });
        }
        catch (Exception ex)
        {
            // Log the error but don't fail the application
            Console.WriteLine($"⚠️ Warning: Redux DevTools initialization failed: {ex.Message}");
        }
    }
});

builder.Services.AddMudServices();
builder.Services.AddAppServices();
builder.Services.AddHttpClient();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler(errorHandlingPath: "/Error"
                            , createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

// AWS S3 is used for file storage - no local directories needed
app.UseStaticFiles();
app.UseAntiforgery();

app.MapRazorComponents<App>()
   .AddInteractiveServerRenderMode();

app.Run();