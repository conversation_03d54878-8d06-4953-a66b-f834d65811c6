﻿using System.Text.Json.Serialization;

namespace HomeFinances.Models.ChatGPT
{
    public class ModelRequestData
    {
        [JsonPropertyName("model")]
        public string Model { get; set; }

        [JsonPropertyName("messages")]
        public List<Message> Messages { get; set; }

        [JsonPropertyName("temperature")]
        public double Temperature { get; set; }

        [JsonPropertyName("max_tokens")]
        public int MaxTokens { get; set; }

        //[JsonPropertyName("session_id")]
        //public Guid SessionId { get; private set; }

        public ModelRequestData()
        {
            //SessionId = Guid.NewGuid();
        }
    }
}