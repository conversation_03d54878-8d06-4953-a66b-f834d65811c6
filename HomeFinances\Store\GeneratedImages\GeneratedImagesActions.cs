﻿namespace HomeFinances.Store.GeneratedImages
{
    public record GeneratedImagesActions
    {
        public record LoadGeneratedImagesAction;
        public record LoadGeneratedImagesSuccessAction(IEnumerable<GeneratedImage> Images);
        public record LoadGeneratedImagesFailureAction(string ErrorMessage);

        public record AddGeneratedImageAction(string Prompt, decimal Cost);
        public record AddGeneratedImageSuccessAction(GeneratedImage Image);
        public record AddGeneratedImageFailureAction(string ErrorMessage);

        public record DeleteGeneratedImageAction(string PartitionKey, string RowKey);
        public record DeleteGeneratedImageSuccessAction(string PartitionKey, string RowKey);
        public record DeleteGeneratedImageFailureAction(string ErrorMessage);

        public record UpdateGeneratedImageAction(string Prompt, decimal Cost);
        public record UpdateGeneratedImageSuccessAction(GeneratedImage Image);
        public record UpdateGeneratedImageFailureAction(string ErrorMessage);

        public record SetRegeneratingImageAction(string Prompt, bool IsRegenerating);
        public record SetRegeneratingImageSuccessAction(string Prompt,GeneratedImage image);
        public record SetRegeneratingImageFailureAction(string Prompt, string ErrorMessage);
    }
}