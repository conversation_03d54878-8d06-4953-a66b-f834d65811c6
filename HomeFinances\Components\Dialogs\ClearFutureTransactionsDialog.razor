@using HomeFinances.Services
@using Fluxor
@using HomeFinances.Store.FutureTransactions

<MudDialog>
    <DialogContent>
        <MudText>Are you sure you want to clear all future transactions? This action cannot be undone.</MudText>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">Cancel</MudButton>
        <MudButton Color="Color.Error" Variant="Variant.Filled" OnClick="Submit">Clear All</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = default!;
    [Inject] private IDispatcher Dispatcher { get; set; } = default!;

    private void Cancel()
    {
        MudDialog.Cancel();
    }

    private void Submit()
    {
        Dispatcher.Dispatch(new FutureTransactionActions.ClearAllFutureTransactionsAction());
        MudDialog.Close(DialogResult.Ok(true));
    }
} 