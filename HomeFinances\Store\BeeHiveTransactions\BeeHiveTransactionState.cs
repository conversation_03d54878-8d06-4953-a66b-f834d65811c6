﻿using Fluxor;
using HomeFinances.Models;

namespace HomeFinances.Store.BeeHiveTransactions
{

    [FeatureState]
    public record BeeHiveTransactionState
    {
        public IEnumerable<BeeHiveTransaction> Transactions { get; set; }
        public LoadingState LoadState { get; set; } = new LoadingState();

        public BeeHiveTransactionState()
        {
            Transactions = new List<BeeHiveTransaction>();
        }
    }
}
