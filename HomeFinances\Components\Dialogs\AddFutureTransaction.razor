﻿@using Fluxor

<style>
    .new-transaction-form {
        display: flex;
        flex-direction: column;
        margin: 25px;
    }
    .add-transaction-button {
        margin: 50px;
        text-align: right;
    }
</style>
<MudDialog>
    <DialogContent>
    <MudForm @ref="form" Model="@_futureBankTransaction">
        <MudTextField Label="Description" @bind-Value="_futureBankTransaction.Description" FullWidth/>
        <MudTextField Label="Amount" @bind-Value="_futureBankTransaction.TransactionAmount" FullWidth/>
        <MudDatePicker Label="Transaction Date" @bind-Date="_futureBankTransaction.TransactionDate" />
        <MudSelect Label="Outgo" @bind-Value="isOutgo">
            <MudSelectItem Value="@(true)"/>
            <MudSelectItem Value="@(false)"/>
        </MudSelect>
        
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton Color="Color.Primary" OnClick="@(() => Submit())">Submit</MudButton>
        <MudButton OnClick="@Cancel">Cancel</MudButton>
    </DialogActions>
</MudDialog>