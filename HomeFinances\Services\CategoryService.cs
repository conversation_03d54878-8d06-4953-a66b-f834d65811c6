using HomeFinances.Repositories;

namespace HomeFinances.Services;

public class CategoryService
{
    private readonly ICategoryRepository _repository;

    public CategoryService(ICategoryRepository repository)
    {
        _repository = repository ?? throw new ArgumentNullException(paramName: nameof(repository));
    }

    public List<string> GetAllCategoriesAndSubcategories()
    {
        var dictResult = _repository.GetAllCategoriesAsync().GetAwaiter().GetResult();
        var dictList = dictResult.Value;
        return dictList;
    }

    public List<string> GetSubCategories(string category)
    {
        var subs = _repository.GetSubCategoriesAsync(category: category).GetAwaiter().GetResult();
        return subs.Value.ToList();
    }

    public void AddCategory(string category
        , string subCategory)
    {
        _repository.AddSubCategoryAsync(category: category
                                        , subCategory: subCategory).GetAwaiter().GetResult();
    }
}