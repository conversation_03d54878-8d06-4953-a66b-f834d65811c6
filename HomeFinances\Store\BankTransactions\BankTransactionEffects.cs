﻿using Fluxor;
using HomeFinances.Services;

namespace HomeFinances.Store.BankTransactions;

public class BankTransactionEffects
{
    private readonly CategoryService _categoryService;
    private readonly IFutureTransactionService _futureTransactionService;
    private readonly ILogger<BankTransactionEffects> _logger;
    private readonly TransactionReadWriteService _transactionReadWriteService;

    public BankTransactionEffects(TransactionReadWriteService transactionReadWriteService
        , CategoryService categoryService
        , IFutureTransactionService futureTransactionService
        , ILogger<BankTransactionEffects> logger)
    {
        _transactionReadWriteService = transactionReadWriteService;
        _categoryService = categoryService;
        _futureTransactionService = futureTransactionService;
        _logger = logger;
        _logger.LogInformation(message: "🔥 BankTransactionEffects initialized - effect handler ready");
        Console.WriteLine("🔥 BankTransactionEffects constructor called - DI working");
    }

    [EffectMethod]
    public async Task HandleLoadTransactionsAction(BankTransactionActions.LoadTransactionsAction action
        , IDispatcher dispatcher)
    {
        Console.WriteLine("🚀 BankTransactionEffects.HandleLoadTransactionsAction - EFFECT METHOD CALLED!");
        _logger.LogInformation(message: "🚀 BankTransactionEffects.HandleLoadTransactionsAction - EFFECT METHOD CALLED!");
        try
        {
            _logger.LogInformation(
                message: "🔥 HandleLoadTransactionsAction called - loading bank transactions from BankTransactions.csv");
            var transactions = await _transactionReadWriteService.ReadBankTransactionsAsync(filePath: "BankTransactions.csv");
            _logger.LogInformation(message: "✅ Successfully loaded {Count} bank transactions"
                                   , transactions.Count);

            dispatcher.Dispatch(action: new BankTransactionActions.LoadTransactionsSuccessAction(Transactions: transactions));
            _logger.LogInformation(message: "✅ Dispatched LoadTransactionsSuccessAction with {Count} transactions"
                                   , transactions.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(exception: ex
                             , message: "❌ Failed to load bank transactions");
            dispatcher.Dispatch(action: new BankTransactionActions.LoadTransactionsFailedAction(ErrorMessage: ex.Message));
        }
    }

    [EffectMethod]
    public async Task HandleMergeBeehiveTransactionsAction(BankTransactionActions.MergeBeehiveTransactionsAction action
        , IDispatcher dispatcher)
    {
        try
        {
            var beehiveTransactions = action.BeeHiveTransactions;
            // Merge and save transactions using the repository
            var mergedTransactions
                = await _transactionReadWriteService.MergeAndSaveBeehiveTransactions(beehiveTransactions: beehiveTransactions
                                                                                     , bankTransactionsFilePath:
                                                                                     "BankTransactions.csv");
            dispatcher.Dispatch(
                action: new BankTransactionActions.MergeBeehiveTransactionsSuccessAction(BankTransactions: mergedTransactions));
        }
        catch (Exception ex)
        {
            dispatcher.Dispatch(
                action: new BankTransactionActions.MergeBeehiveTransactionsFailedAction(ErrorMessage: ex.Message));
        }
    }
}