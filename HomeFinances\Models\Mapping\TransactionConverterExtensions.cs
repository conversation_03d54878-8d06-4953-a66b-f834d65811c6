﻿namespace HomeFinances.Models.Mapping
{
    public static class TransactionConverterExtensions
    {
        public static FutureBankTransaction ToFutureTransaction(this BankTransaction bankTransaction)
        {
            if (bankTransaction == null)
            {
                throw new ArgumentNullException(nameof(bankTransaction), "The BankTransaction cannot be null.");
            }

            var futureTransaction = new FutureBankTransaction
            {
                TransactionAmount = bankTransaction.TransactionAmount,
                Description = $"{bankTransaction.Description}".Trim(),
                Taxonomy = bankTransaction.Taxonomy,
                TransactionDate = bankTransaction.TransactionDate
            };

            return futureTransaction;
        }

        public static BankTransaction ToBankTransaction(this BeeHiveTransaction beeHiveTransaction)
        {
            return new BankTransaction
            {
                Account = beeHiveTransaction.Account,
                Description = beeHiveTransaction.Description,
                Balance = beeHiveTransaction.Balance,
                Taxonomy = beeHiveTransaction.Taxonomy,
                TransactionDate = beeHiveTransaction.TransactionDate,
                TransactionAmount = beeHiveTransaction.TransactionAmount
            };
        }

        public static List<BankTransaction> ToBankTransactions(this List<BeeHiveTransaction> beeHiveTransactions)
        {
            return beeHiveTransactions.Select(x => x.ToBankTransaction()).ToList();
        }
    }
}