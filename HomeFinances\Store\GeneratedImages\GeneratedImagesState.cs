﻿using Fluxor;

namespace HomeFinances.Store.GeneratedImages
{
    public record GeneratedImage(string Prompt, string ImageUrl, decimal Cost, string PartitionKey = "ImageGenerations");

    [FeatureState]
    public record GeneratedImagesState
    {
        public List<GeneratedImage> Images { get; init; }
        public string PartitionKey { get; init; }
        public bool IsLoading { get; init; }
        public string ErrorMessage { get; init; }
        public Dictionary<string, bool> RegeneratingImages { get; init; }

        public GeneratedImagesState()
        {
            Images = new List<GeneratedImage>();
            IsLoading = false;
            ErrorMessage = "";
            RegeneratingImages = new Dictionary<string, bool>();
        }
    }
}