﻿namespace HomeFinances.Models
{
    public record BankTransaction
    {
        public Guid Id { get; set; }
        public string Account { get; set; }
        public DateTime? TransactionDate { get; set; }
        public decimal TransactionAmount { get; set; }
        public string Description { get; set; }
        public decimal Balance { get; set; }
        public Taxonomy Taxonomy { get; set; }
        public Guid RecurringTransactionId { get; set; }
        public string BankCode { get; set; }

        public BankTransaction()
        {
            Id = Guid.NewGuid();
            Account = string.Empty;
            Description = string.Empty;
            Taxonomy = new Taxonomy();
            BankCode = string.Empty;
        }

        public override string ToString()
        {
            return $"{Id} {TransactionDate} {Description} {TransactionAmount}";
        }
    }
}
