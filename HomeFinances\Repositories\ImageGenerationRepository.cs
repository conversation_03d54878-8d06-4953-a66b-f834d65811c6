using Azure;
using Azure.Data.Tables;
using CSharpFunctionalExtensions;

namespace HomeFinances.Repositories;

public class ImageGenerationRepository
{
    private readonly TableClient _tableClient;

    public ImageGenerationRepository(TableServiceClient tableServiceClient)
    {
        var tableName = "GeneratedImages";
        _tableClient = tableServiceClient.GetTableClient(tableName: tableName);
        _tableClient.CreateIfNotExists();
    }

    public async Task<Result> AddOrUpdateImageGenerationAsync(string partitionKey
        , string rowKey
        , decimal cost
        , string imageUrl)
    {
        var entity = new ImageGenerationEntity(rowKey: rowKey
                                               , cost: cost
                                               , imageUrl: imageUrl);
        entity.PartitionKey = partitionKey;
        await _tableClient.UpsertEntityAsync(entity: entity);
        return Result.Success();
    }

    public async Task<Result<ImageGenerationEntity>> GetImageGenerationAsync(string partitionKey
        , string rowKey)
    {
        try
        {
            var response = await _tableClient.GetEntityAsync<ImageGenerationEntity>(partitionKey: partitionKey
                                                                                    , rowKey: rowKey);
            return Result.Success(value: response.Value); // Return success result with entity
        }
        catch (RequestFailedException ex) when (ex.Status == 404)
        {
            return Result.Failure<ImageGenerationEntity>(error: "Entity not found");
        }
    }

    public async Task<Result> DeleteImageGenerationAsync(string partitionKey
        , string rowKey)
    {
        try
        {
            await _tableClient.DeleteEntityAsync(partitionKey: partitionKey
                                                 , rowKey: rowKey);
        }
        catch (Exception e)
        {
            return Result.Failure(error: $"Failed to delete entity: {e.Message}");
        }

        return Result.Success();
    }

    public async Task<Result<List<ImageGenerationEntity>>> GetAllImageGenerationsAsync()
    {
        var entities = new List<ImageGenerationEntity>();

        await foreach (var entity in _tableClient.QueryAsync<ImageGenerationEntity>()) entities.Add(item: entity);

        return Result.Success(value: entities); // Return success result with list of entities
    }
}