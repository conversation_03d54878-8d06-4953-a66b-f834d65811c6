﻿using System.Globalization;
using Azure;
using Azure.Data.Tables;

public class ImageGenerationEntity : ITableEntity
{
    public ImageGenerationEntity()
    {
        PartitionKey = "ImageGenerations";
        RowKey = string.Empty;
        Cost = "0";
        ImageUrl = string.Empty;
        RevisedPrompt = string.Empty;
        Timestamp = DateTimeOffset.UtcNow;
        ETag = ETag.All;
    }

    public ImageGenerationEntity(string rowKey, decimal cost, string imageUrl)
    {
        PartitionKey = "ImageGenerations";
        RowKey = rowKey;
        Cost = cost.ToString(CultureInfo.InvariantCulture);
        ImageUrl = imageUrl;
        RevisedPrompt = string.Empty;
        Timestamp = DateTimeOffset.UtcNow;
        ETag = ETag.All;
    }

    public string PartitionKey { get; set; }
    public string RowKey { get; set; }
    public string Cost { get; set; }  // Store as string
    public string ImageUrl { get; set; }
    public string RevisedPrompt { get; set; }
    public DateTimeOffset? Timestamp { get; set; }
    public ETag ETag { get; set; }

    public decimal CostDecimal
    {
        get => decimal.Parse(Cost, CultureInfo.InvariantCulture);
        set => Cost = value.ToString(CultureInfo.InvariantCulture);
    }
}