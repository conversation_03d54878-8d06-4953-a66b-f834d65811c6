@using ChartJs.Blazor
@using ChartJs.Blazor.BarChart
@using ChartJs.Blazor.LineChart
@using ChartJs.Blazor.Common
@using ChartJs.Blazor.Common.Axes
@using ChartJs.Blazor.Common.Enums
@using ChartJs.Blazor.Common.Time
@using ChartJs.Blazor.Util
@using System.Linq
@using HomeFinances.Models // Your namespace for RecurringTransaction

<h3>Recurring Transactions Chart</h3>

@if (_barConfig != null)
{
    <Chart Config="_barConfig"></Chart>
}
else
{
    <p><em>Loading chart data...</em></p>
}

@code {
    [Parameter]
    public List<RecurringTransaction>? RecurringTransactions { get; set; }

    private BarConfig? _barConfig;

    protected override void OnParametersSet()
    {
        ProcessChartData();
    }

    private void ProcessChartData()
    {
        if (RecurringTransactions == null || !RecurringTransactions.Any())
        {
            _barConfig = null;
            return;
        }

        var dailySpending = new Dictionary<int, decimal>();
        var cumulativeSpending = new Dictionary<int, decimal>();
        decimal runningTotal = 0;

        // Initialize daily spending for all days
        for (int i = 1; i <= 31; i++)
        {
            dailySpending[i] = 0;
        }

        // Calculate daily spending
        foreach (var transaction in RecurringTransactions)
        {
            if (transaction.DayOfMonth > 0 && transaction.DayOfMonth <= 31)
            {
                dailySpending[transaction.DayOfMonth] += transaction.IsOutgo ? transaction.Amount : -transaction.Amount;
            }
        }

        // Calculate cumulative spending
        for (int i = 1; i <= 31; i++)
        {
            runningTotal += dailySpending[i];
            cumulativeSpending[i] = runningTotal;
        }

        _barConfig = new BarConfig
        {
            Options = new BarOptions
            {
                Responsive = true,
                Title = new OptionsTitle
                {
                    Display = true,
                    Text = "Daily and Cumulative Recurring Transactions"
                },
                Scales = new BarScales
                {
                    YAxes = new List<CartesianAxis>
                    {
                        new LinearCartesianAxis
                        {
                            ID = "y-axis-1",
                            Position = ChartJs.Blazor.Common.Enums.Position.Left,
                            ScaleLabel = new ScaleLabel
                            {
                                LabelString = "Daily Amount ($)",
                                Display = true
                            }
                        },
                        new LinearCartesianAxis
                        {
                            ID = "y-axis-2",
                            Position = ChartJs.Blazor.Common.Enums.Position.Right,
                            ScaleLabel = new ScaleLabel
                            {
                                LabelString = "Cumulative Amount ($)",
                                Display = true
                            }
                        }
                    }
                }
            }
        };

        var barDataset = new BarDataset<decimal>(dailySpending.Values.ToList())
        {
            Label = "Daily Spending",
            BackgroundColor = "#4CAF50",
            BorderColor = "#388E3C",
            BorderWidth = 1,
            YAxisId = "y-axis-1"
        };

        var lineDataset = new LineDataset<decimal>(cumulativeSpending.Values.ToList())
        {
            Label = "Cumulative Spending",
            BorderColor = "#2196F3",
            Fill = false,
            YAxisId = "y-axis-2"
        };

        foreach (var i in Enumerable.Range(1, 31))
        {
            _barConfig.Data.Labels.Add(i.ToString());
        }
        _barConfig.Data.Datasets.Add(barDataset);
        _barConfig.Data.Datasets.Add(lineDataset);
    }
}