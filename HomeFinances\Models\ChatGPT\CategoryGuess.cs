﻿namespace HomeFinances.Models.ChatGPT;

public class CategoryGuess
{
    public CategoryGuess()
    {
        Category = string.Empty;
        SubCategory = string.Empty;
        Notes = string.Empty;
        Tags = new List<string>();
    }

    public string Category { get; set; }
    public string SubCategory { get; set; }
    public string Notes { get; set; }
    public int Confidence { get; set; }
    public List<string> Tags { get; set; }
}