﻿using Serilog;
using Serilog.Filters;

public static class LoggingExtensions
{
    public static void AddLogging(this WebApplicationBuilder builder)
    {
        builder.Host.UseSerilog((context, services, configuration) => configuration
            .ReadFrom.Configuration(context.Configuration)
            .MinimumLevel.Information()
            .WriteTo.File(@"logs\log.txt", rollingInterval: RollingInterval.Day));
    }
}