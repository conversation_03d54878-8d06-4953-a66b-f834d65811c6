/*
	This CSS file matches the color scheme from MudBlazor to Bootstrap when utilized for authentication.
	The file remains available at all times for demonstration purposes,
	but it is exclusively employed in the 'App.razor' component when authentication is enabled.
*/

.btn-primary {
    text-transform: uppercase;
    --bs-btn-bg: var(--mud-palette-primary) !important;
    --bs-btn-hover-bg: var(--mud-palette-primary-darken) !important;
}

.nav-pills {
    --bs-nav-pills-link-active-bg: var(--mud-palette-primary) !important;
}

.nav {
    --bs-nav-link-color: var(--mud-palette-primary) !important;
    --bs-nav-link-hover-color: var(--mud-palette-primary-darken) !important;
}