﻿namespace HomeFinances.Models
{
    public record BeeHiveTransaction : BankTransaction
    {
        public string Suffix { get; set; }
        public string SequenceNumber { get; set; }
        public string ExtendedDescription { get; set; }
        public DateTime? ElectronicTransactionDate { get; set; }
        public DateTime? ElectronicTransactionTime { get; set; }

        public BeeHiveTransaction() : base()
        {
            Suffix = string.Empty;
            SequenceNumber = string.Empty;
            ExtendedDescription = string.Empty;
        }
    }
}