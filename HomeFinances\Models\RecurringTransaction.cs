﻿using HomeFinances.Models;

public class RecurringTransaction
{
    public Guid Id { get; set; }
    public string Description { get; set; }
    public int DayOfMonth { get; set; }
    public Taxonomy Taxonomy { get; set; }
    public DateTime? IgnoreUntil { get; set; }
    public DateOnly EndDate { get; set; }
    public DateTime? EndDateTime
    {
        get => EndDate.ToDateTime(TimeOnly.MinValue);
        set
        {
            if (value.HasValue)
                EndDate = DateOnly.FromDateTime(value.Value);
            else
                EndDate = default;
        }
    }
    public decimal Amount { get; set; } = 0M;
    public bool IsOutgo { get; set; } = true;

    public RecurringTransaction()
    {
        Id = Guid.NewGuid();
        Description = string.Empty;
        Taxonomy = new Taxonomy();
    }

    public bool IsEquivalentToBankTransaction(BankTransaction bankTransaction)
    {
        if (bankTransaction == null) return false;
        return this.Amount == bankTransaction.TransactionAmount &&
               this.DayOfMonth == bankTransaction.TransactionDate?.Day;
    }

    public bool IsEquivalentToFutureTransaction(FutureBankTransaction futureTransaction)
    {
        if (futureTransaction == null || futureTransaction.TransactionDate == null) return false;
        return this.Amount == futureTransaction.TransactionAmount &&
               this.DayOfMonth == futureTransaction.TransactionDate.Value.Day;
    }
    public override string ToString()
    {
        return $"{Description} DayOfMonth: {DayOfMonth}, Amount: {Amount:C}, IsOutgo: {IsOutgo}, EndDate: {EndDate}, IgnoreUntil: {IgnoreUntil?.ToShortDateString()}";
    }
}