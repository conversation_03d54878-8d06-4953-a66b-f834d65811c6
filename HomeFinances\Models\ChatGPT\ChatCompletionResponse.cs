﻿using System.Text.Json.Serialization;

namespace HomeFinances.Models.ChatGPT
{
    public class ChatCompletionResponse
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("object")]
        public string Object { get; set; }

        [JsonPropertyName("created")]
        public long Created { get; set; }

        [JsonPropertyName("model")]
        public string Model { get; set; }

        [JsonPropertyName("choices")]
        public List<Choice> Choices { get; set; }

        [JsonPropertyName("usage")]
        public Usage Usage { get; set; }

        [JsonPropertyName("system_fingerprint")]
        public string SystemFingerprint { get; set; }

        public ChatCompletionResponse()
        {
            Id = string.Empty;
            Object = string.Empty;
            Model = string.Empty;
            Choices = new List<Choice>();
            Usage = new Usage();
            SystemFingerprint = string.Empty;
        }
    }

    // Make sure all other classes (Choice, Usage, etc.) are also correctly annotated or match the JSON structure.
}