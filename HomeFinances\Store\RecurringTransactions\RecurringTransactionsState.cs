﻿using Fluxor;
using HomeFinances.Models;

namespace HomeFinances.Store.RecurringTransactions
{
    [FeatureState]
    public record RecurringTransactionsState
    {
        public IEnumerable<RecurringTransaction> RecurringTransactions { get; set; }
        public LoadingState Loading { get; set; }

        public RecurringTransactionsState()
        {
            RecurringTransactions = new List<RecurringTransaction>();
            Loading = new LoadingState();
        }
    }
}
