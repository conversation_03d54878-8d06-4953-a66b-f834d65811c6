using Fluxor;
using HomeFinances.Components.Dialogs;
using HomeFinances.Models;
using HomeFinances.Services;
using HomeFinances.Store.BeeHiveTransactions;
using HomeFinances.Store.FutureTransactions;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using MudBlazor;

namespace HomeFinances.Components.Pages;

public partial class FutureTransactions : ComponentBase, IDisposable
{
    private IDialogReference? _dialogReference;
    private bool _isInitialized = false;

    // Sample category and subcategory data
    private List<string> Categories = new() { "Food", "Utilities", "Entertainment" };

    private bool isDeleting = false;
    private HashSet<FutureBankTransaction> selectedTransactions = new();

    private List<string> SubCategories = new() { "Groceries", "Electricity", "Movies" };

    private double windowWidth;
    [Inject] private IState<BeeHiveTransactionState> BeehiveTransactionState { get; set; } = default!;
    [Inject] private IState<FutureTransactionState> FutureTransactionState { get; set; } = default!;
    [Inject] private IDispatcher Dispatcher { get; set; } = default!;
    [Inject] private IFutureTransactionService FutureTransactionService { get; set; } = default!;
    [Inject] private IDialogService DialogService { get; set; } = default!;
    [Inject] private IJSRuntime JsRuntime { get; set; } = default!;

    private IEnumerable<FutureBankTransaction> futureTransactions =>
        FutureTransactionState?.Value?.FutureTransactions?.ToList() ?? new List<FutureBankTransaction>();

    private bool IsAllSelected => futureTransactions.Any() && selectedTransactions.Count == futureTransactions.Count();
    private bool IsIndeterminate => selectedTransactions.Count > 0 && selectedTransactions.Count < futureTransactions.Count();

    // Callback properties to enable method group binding in Razor without complex lambdas
    private Action<bool> ToggleSelectAllCallback => ToggleSelectAll;

    private BeeHiveTransaction? LatestTransaction
    {
        get
        {
            var latest = BeehiveTransactionState.Value.Transactions.OrderByDescending(keySelector: t => t.TransactionDate)
               .FirstOrDefault();
            Console.WriteLine(
                value:
                $"LatestTransaction called. Count: {BeehiveTransactionState.Value.Transactions.Count()}, Latest: {latest?.Balance:C}");
            return latest;
        }
    }

    public void Dispose()
    {
        BeehiveTransactionState.StateChanged -= OnStateHasChanged;
        FutureTransactionState.StateChanged -= OnStateHasChanged;
    }

    private void OnRowChecked(FutureBankTransaction item
        , bool isChecked)
    {
        if (isChecked)
            selectedTransactions.Add(item: item);
        else
            selectedTransactions.Remove(item: item);
    }

    private void ToggleSelectAll(bool isChecked)
    {
        if (isChecked)
            selectedTransactions = futureTransactions.ToHashSet();
        else
            selectedTransactions.Clear();
    }

    private Action<bool> GetRowCheckedCallback(FutureBankTransaction item)
    {
        return value => OnRowChecked(item: item
                                     , isChecked: value);
    }

    private async Task DeleteSelected()
    {
        if (!selectedTransactions.Any())
            return;
        isDeleting = true;
        StateHasChanged();
        var itemsToDelete = selectedTransactions.ToList();
        selectedTransactions.Clear();
        foreach (var item in itemsToDelete) await FutureTransactionService.DeleteTransactionAsync(bankTransactionToDelete: item);
        Dispatcher.Dispatch(action: new FutureTransactionActions.LoadFutureTransactionsAction());
        isDeleting = false;
        StateHasChanged();
    }

    protected override void OnInitialized()
    {
        if (!_isInitialized)
        {
            _isInitialized = true;
            BeehiveTransactionState.StateChanged += OnStateHasChanged;
            FutureTransactionState.StateChanged += OnStateHasChanged;

            Console.WriteLine(
                value: $"Initializing. BeehiveTransaction count: {BeehiveTransactionState.Value.Transactions.Count()}");
            var latest = BeehiveTransactionState.Value.Transactions.OrderByDescending(keySelector: t => t.TransactionDate)
               .FirstOrDefault();
            Console.WriteLine(value: $"Initial latest transaction: {latest?.TransactionDate:MM/dd/yyyy} - {latest?.Balance:C}");

            // FutureTransactions component only dispatches its own events
            // It listens to BeehiveTransactionState but doesn't dispatch BeehiveTransaction actions
            // The BankTransactions component or other appropriate component should handle BeehiveTransaction loading

            if (FutureTransactionState.Value.Loading.Status == LoadingStatusEnum.NotLoaded)
                Dispatcher.Dispatch(action: new FutureTransactionActions.LoadFutureTransactionsAction());
        }

        base.OnInitialized();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JsRuntime.InvokeVoidAsync(identifier: "registerResizeListener"
                                            , DotNetObjectReference.Create(value: this));
            await GetWindowWidth();
        }
    }

    [JSInvokable]
    public async Task OnBrowserResize()
    {
        await GetWindowWidth();
    }

    private async Task GetWindowWidth()
    {
        windowWidth = await JsRuntime.InvokeAsync<double>(identifier: "blazorResize.getInnerWidth");
        StateHasChanged();
    }

    private Task<IEnumerable<string>> SearchCategories(string value)
    {
        return Task.FromResult(result: Categories.Where(predicate: x => x.Contains(value: value
                                                                                   , comparisonType: StringComparison
                                                                                      .InvariantCultureIgnoreCase))
                                  .AsEnumerable());
    }

    private Task<IEnumerable<string>> SearchSubCategories(string value)
    {
        return Task.FromResult(result: SubCategories.Where(predicate: x => x.Contains(value: value
                                                               , comparisonType: StringComparison.InvariantCultureIgnoreCase))
                                  .AsEnumerable());
    }

    private void OnDateChanged(DateTime? newDate
        , FutureBankTransaction bankTransactionToUpdate)
    {
        var updatedTransaction = bankTransactionToUpdate;
        updatedTransaction.TransactionDate = newDate;
        Dispatcher.Dispatch(action: new FutureTransactionActions.UpdateFutureTransactionAction(transaction: updatedTransaction));
    }

    private async Task OnDescriptionChanged(string newValue
        , FutureBankTransaction context)
    {
        context.Description = newValue;
        await UpdateBankTransaction(transaction: context);
    }

    private async Task OnCategoryChanged(string newValue
        , FutureBankTransaction context)
    {
        context.Taxonomy.Category = newValue;
        Console.WriteLine(value: $"Category changed to: {newValue} for transaction ID {context.Id}");
        await UpdateBankTransaction(transaction: context);
    }

    private async Task OnAmountChanged(decimal newValue
        , FutureBankTransaction context)
    {
        context.TransactionAmount = newValue;
        Console.WriteLine(value: $"Category changed to: {newValue} for transaction ID {context.Id}");
        await UpdateBankTransaction(transaction: context);
    }

    private async Task OnSubCategoryChanged(string newValue
        , FutureBankTransaction context)
    {
        context.Taxonomy.SubCategory = newValue;
        Console.WriteLine(value: $"SubCategory changed to: {newValue} for transaction ID {context.Id}");
        await UpdateBankTransaction(transaction: context);
    }

    private async Task UpdateBankTransaction(FutureBankTransaction transaction)
    {
        Dispatcher.Dispatch(action: new FutureTransactionActions.UpdateFutureTransactionAction(transaction: transaction));
    }

    private string TruncateDescription(string description)
    {
        if (string.IsNullOrEmpty(value: description)) return string.Empty;
        return description.Length > 10
            ? description.Substring(startIndex: 0
                                    , length: 10) + "…"
            : description;
    }

    private void OnStateHasChanged(object? sender
        , EventArgs e)
    {
        StateHasChanged();
    }

    private void DeleteItem(FutureBankTransaction bankTransactionToDelete)
    {
        Dispatcher.Dispatch(
            action: new FutureTransactionActions.DeleteFutureTransactionAction(transaction: bankTransactionToDelete));
    }

    private string GetEnabledIcon(FutureBankTransaction bankTransaction)
    {
        return bankTransaction.Enabled
            ? Icons.Material.Rounded.CheckBox
            : Icons.Material.Rounded.CheckBoxOutlineBlank;
    }

    private void ToggleEnabled(FutureBankTransaction bankTransactionToToggle)
    {
        var updatedTransaction = bankTransactionToToggle;
        updatedTransaction.Enabled = !updatedTransaction.Enabled;
        InvokeAsync(workItem: StateHasChanged);
        Dispatcher.Dispatch(action: new FutureTransactionActions.UpdateFutureTransactionAction(transaction: updatedTransaction));
    }

    public static bool AreTransactionsEquivalent(FutureBankTransaction x
        , FutureBankTransaction y)
    {
        if (x == null && y == null) return true;
        if (x == null || y == null) return false;

        return x.TransactionAmount == y.TransactionAmount &&
               string.Equals(a: x.Description
                             , b: y.Description);
    }

    private void ShowAddTransactionDialog()
    {
        var options = new DialogOptions { CloseButton = false, MaxWidth = MaxWidth.Small, FullWidth = true };
        var parameters = new DialogParameters();

        _dialogReference = DialogService.Show<AddFutureTransaction>(title: "Add Future Transaction"
                                                                    , parameters: parameters
                                                                    , options: options);

        InvokeAsync(workItem: StateHasChanged);
    }

    private void CloseDialog()
    {
        _dialogReference?.Close();
        InvokeAsync(workItem: StateHasChanged);
    }

    protected void Dispose(bool disposed)
    {
        BeehiveTransactionState.StateChanged -= OnStateHasChanged;
        FutureTransactionState.StateChanged -= OnStateHasChanged;
    }

    private async Task ShowClearAllDialog()
    {
        var parameters = new DialogParameters();
        var dialog = await DialogService.ShowAsync<ClearFutureTransactionsDialog>(title: "Clear All Future Transactions"
                                                                                  , parameters: parameters);
        var result = await dialog.Result;

        if (!result.Canceled) await LoadData();
    }

    private async Task LoadData()
    {
        // Reload the future transactions state
        await Task.CompletedTask; // This will be handled by the Fluxor state management
        StateHasChanged();
    }
}