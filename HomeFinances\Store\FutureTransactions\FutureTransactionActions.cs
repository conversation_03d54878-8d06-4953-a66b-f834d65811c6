﻿using HomeFinances.Models;

namespace HomeFinances.Store.FutureTransactions
{
    public class FutureTransactionActions
    {
        public record LoadFutureTransactionsAction;
        public record LoadFutureTransactionsSuccessAction(IEnumerable<FutureBankTransaction> Transactions);
        public record LoadFutureTransactionsFailedAction(string ErrorMessage);

        public record UpdateFutureTransactionAction(FutureBankTransaction transaction);
        public record UpdateFutureTransactionSucceededAction(List<FutureBankTransaction> transactions);
        public record UpdateFutureTransactionFailedAction(string ErrorMessage);

        //public record UpdateFutureTransactionsAction(IEnumerable<FutureBankTransaction> Transactions);
        //public record UpdateFutureTransactionsSuccessAction(IEnumerable<FutureBankTransaction> Transactions);
        //public record UpdateFutureTransactionsFailedAction(string ErrorMessage);

        public record DeleteFutureTransactionAction(FutureBankTransaction transaction);

        public record DeleteFutureTransactionSucceededAction(List<FutureBankTransaction> transactions);

        public record DeleteFutureTransactionFailedAction(string ErrorMessage);

        public record ClearAllFutureTransactionsAction;
        public record ClearAllFutureTransactionsSucceededAction;
        public record ClearAllFutureTransactionsFailedAction(string ErrorMessage);

        public record DisableFutureTransactionAction(FutureBankTransaction transaction);
        public record UpdateFutureTransactionDateAction(FutureBankTransaction transaction, DateTime newDate);
    }
}
