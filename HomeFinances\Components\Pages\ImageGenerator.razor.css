﻿body {
}
.image-tile {
    width: 600px;
    height: 600px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 16px;
    border: 3px solid #ccc;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

@media print {
    /* Hide elements that should not appear in print */
    .button, .navbar {
        display: none;
    }

    /* Adjust the layout for print */
    .tile {
        border: 1px solid black;
        margin: 0; /* Reduce margin to fit more content */
        padding: 10px;
        page-break-inside: avoid; /* Avoid breaking tiles across pages */
    }
    .ctrl-objects {
        display: none;
    }

    body {
        width: 100%;
        margin: 0;
    }

    /* You might want to adjust the width of elements to ensure they fit on the printed page */
    .grid-container {
        width: 100%;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); /* Adjust for a more linear or compact layout if needed */
    }
}