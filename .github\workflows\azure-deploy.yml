name: Build and deploy ASP.Net Core app to Azure Web App - HomeFinancesGordon

on:
  push:
    branches:
      - azure
  workflow_dispatch:

jobs:
  build:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v4.1.1
      - name: Set up .NET Core
        uses: actions/setup-dotnet@v4.0.1
        with:
          dotnet-version: '8.0'
          include-prerelease: true
      - name: Build with dotnet
        run: dotnet build --configuration Release
      - name: dotnet publish
        run: dotnet publish HomeFinances/HomeFinances.csproj -c Release -o ./publish
      - name: Upload artifact for deployment job
        uses: actions/upload-artifact@v4.0.0
        with:
          name: .net-app
          path: ./publish

  deploy:
    runs-on: windows-latest
    needs: build
    environment:
      name: 'Production'
      url: ${{ steps.deploy-to-webapp.outputs.webapp-url }}
    permissions:
      id-token: write # Required for requesting the JWT

    steps:
      - name: Download artifact from build job
        uses: actions/download-artifact@v4.1.1
        with:
          name: .net-app
          path: ./publish

      - name: Login to Azure
        uses: azure/login@v2
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - name: Deploy to Azure Web App
        id: deploy-to-webapp
        uses: azure/webapps-deploy@v3
        with:
          app-name: 'HomeFinancesGordon'
          package: ./publish
          restart: true