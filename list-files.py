import os
import argparse
import re
import string
from pathlib import Path

def sanitize_filename(filename):
    """Removes characters that are typically unsafe for filenames."""
    # Allow letters, numbers, underscores, hyphens, periods
    valid_chars = "-_.() %s%s" % (string.ascii_letters, string.digits)
    sanitized = ''.join(c for c in filename if c in valid_chars)
    # Replace spaces with underscores (optional, but common)
    sanitized = sanitized.replace(' ', '_')
    if not sanitized:
        sanitized = "_empty_filename_"
    return sanitized

def create_header(title, width=80):
    """Creates an ASCII header block for a given title."""
    border = '#' * width
    title_line = f"# {title.center(width - 4)} #"
    # Ensure title fits, truncate if necessary
    if len(title) > width - 4:
        title_line = f"# {title[:width - 7]}... #"

    return f"\n{border}\n{title_line}\n{border}\n\n"

def find_files(start_dir, extensions, skip_pattern_compiled):
    """Walks the directory tree and finds files matching criteria."""
    found_files_paths = []
    start_path = Path(start_dir).resolve() # Ensure absolute path

    for root, dirs, files in os.walk(start_dir, topdown=True):
        # Prune directories to skip (modify dirs in-place)
        dirs[:] = [d for d in dirs if not skip_pattern_compiled.search(Path(root).joinpath(d).as_posix())]

        current_path = Path(root).resolve()

        for file in files:
            if file.endswith(tuple(extensions)):
                full_path = current_path / file
                relative_path = full_path.relative_to(start_path)
                found_files_paths.append(relative_path.as_posix()) # Store posix path for consistency

    return sorted(found_files_paths) # Sort for consistent output

def generate_hierarchy_line(relative_path_str):
    """Generates the '+---' formatted line for the file list."""
    path_obj = Path(relative_path_str)
    depth = len(path_obj.parts)
    indent = '  ' * (depth - 1) if depth > 1 else ''
    prefix = '+--- ' if depth > 0 else ''
    return f"{indent}{prefix}{path_obj.name}"

def read_file_list(list_filename):
    """Reads a list of relative file paths from a file."""
    if not Path(list_filename).is_file():
        print(f"Error: File list '{list_filename}' not found.")
        return None
    try:
        with open(list_filename, 'r', encoding='utf-8') as f:
            # Read lines, strip whitespace, filter empty lines
            paths = [line.strip() for line in f if line.strip()]
        return paths
    except Exception as e:
        print(f"Error reading file list '{list_filename}': {e}")
        return None

def main():
    parser = argparse.ArgumentParser(
        description='Generates a text file listing specified files hierarchically '
                    'and concatenates their content.',
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument(
        '-e', '--ext',
        default='.cs',
        help='Comma-separated list of file extensions to include (e.g., ".cs,.txt,.json"). Default: .cs'
    )
    parser.add_argument(
        '-s', '--skip',
        default=r'[\\/](bin|obj)$', # Regex to match '/bin' or '/obj' or '\bin' or '\obj' at the end of a path segment
        help='Regex pattern for directories to skip. Uses POSIX paths for matching.\n'
             'Default: [\\\\/](bin|obj)$ (skips directories named "bin" or "obj").\n'
             'Note: Need to escape backslashes for shell if entering complex patterns.'
    )
    parser.add_argument(
        '-f', '--fileName',
        default=None,
        help='Output filename. Default: {CurrentDirectoryName}Files.txt'
    )
    parser.add_argument(
        '--fromFileList',
        default=None,
        metavar='LIST_FILE',
        help='Specify a file containing a list of relative file paths to include.\n'
             'If provided, only files listed in this file will be processed.\n'
             'Directory traversal based on --ext and --skip is ignored.'
    )

    args = parser.parse_args()

    # --- Setup ---
    start_dir = '.'
    start_path_obj = Path(start_dir).resolve()

    # Process extensions
    extensions = [e.strip() for e in args.ext.split(',') if e.strip()]
    if not extensions:
        print("Error: No valid extensions provided.")
        return

    # Compile skip pattern
    try:
        skip_pattern_compiled = re.compile(args.skip)
    except re.error as e:
        print(f"Error: Invalid regex pattern for --skip: {e}")
        return

    # Determine output filename
    if args.fileName:
        output_filename = args.fileName
    else:
        current_dir_name = sanitize_filename(start_path_obj.name)
        output_filename = f"{current_dir_name}Files.txt"

    # --- Get File List ---
    relative_file_paths = []
    if args.fromFileList:
        print(f"Reading file list from: {args.fromFileList}")
        listed_paths = read_file_list(args.fromFileList)
        if listed_paths is None:
            return # Error already printed

        # Validate existence and store if valid
        for rel_path in listed_paths:
            full_path = start_path_obj / rel_path
            if full_path.is_file():
                relative_file_paths.append(rel_path)
            else:
                print(f"Warning: File '{rel_path}' listed in '{args.fromFileList}' not found or is not a file. Skipping.")
        if not relative_file_paths:
            print("No valid files found from the provided list.")
            return
        # Sort the validated list from the file for consistent hierarchy
        relative_file_paths.sort(key=lambda p: Path(p).parts)

    else:
        print(f"Scanning directory: {start_path_obj}")
        print(f"Including extensions: {', '.join(extensions)}")
        print(f"Skipping directories matching regex: {args.skip}")
        relative_file_paths = find_files(start_dir, extensions, skip_pattern_compiled)
        if not relative_file_paths:
            print("No matching files found.")
            return

    # --- Generate Output ---
    print(f"Writing output to: {output_filename}")
    try:
        with open(output_filename, 'w', encoding='utf-8') as f_out:
            # Section 1: File Hierarchy
            f_out.write(create_header("Files"))
            if not relative_file_paths:
                f_out.write("No matching files found.\n")
            else:
                # Build hierarchy dictionary for sorting and printing
                hierarchy = {}
                for rel_path_str in relative_file_paths:
                    parts = Path(rel_path_str).parts
                    current_level = hierarchy
                    for i, part in enumerate(parts):
                        is_file = (i == len(parts) - 1)
                        key = ('file', part) if is_file else ('dir', part)
                        if key not in current_level:
                             current_level[key] = {} if not is_file else rel_path_str
                        if not is_file:
                            current_level = current_level[key]

                # Recursive function to print hierarchy
                def print_level(level_dict, depth=0):
                    # Sort items: directories first, then files, alphabetically within type
                    sorted_items = sorted(level_dict.items(), key=lambda item: (item[0][0] != 'dir', item[0][1]))
                    indent = '  ' * depth
                    prefix = '+--- ' if depth >=0 else '' # Always show prefix for top level? Adjust as needed. Let's make it always show for files/dirs under root.
                    if depth == 0: prefix = ''


                    for key, value in sorted_items:
                        type, name = key
                        line_prefix = '  ' * depth + '+--- '
                        f_out.write(f"{line_prefix}{name}\n")
                        if type == 'dir':
                            print_level(value, depth + 1)

                # Simplified hierarchy print - matching requested format directly from sorted list
                last_parts = []
                for rel_path_str in relative_file_paths:
                    path_obj = Path(rel_path_str)
                    parts = path_obj.parts
                    depth = len(parts)
                    indent = '  ' * (depth - 1) if depth > 1 else ''
                    prefix = '+--- '
                    f_out.write(f"{indent}{prefix}{path_obj.name}\n")


            # Section 2: File Contents
            f_out.write("\n") # Add space before content blocks
            for rel_path_str in relative_file_paths:
                f_out.write(create_header(rel_path_str))
                full_path = start_path_obj / rel_path_str
                try:
                    with open(full_path, 'r', encoding='utf-8', errors='ignore') as f_in:
                        content = f_in.read()
                        f_out.write(content)
                        # Ensure a newline after content, even if file doesn't end with one
                        if content and not content.endswith('\n'):
                            f_out.write('\n')
                except Exception as e:
                    f_out.write(f"\n--- ERROR READING FILE: {e} ---\n")
                f_out.write("\n") # Add blank line after each file's content

        print("Done.")

    except Exception as e:
        print(f"Error writing output file '{output_filename}': {e}")


if __name__ == "__main__":
    main()