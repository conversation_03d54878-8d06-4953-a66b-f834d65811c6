<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <!-- Clear all external package sources and ensure only nuget.org is used -->
  <packageSources>
    <clear />
    <add key="nuget.org" value="https://api.nuget.org/v3/index.json" protocolVersion="3" />
  </packageSources>
  <!-- Override default VS fallback package folders to avoid missing paths in non-Windows environments -->
  <fallbackPackageFolders>
    <!-- Clear all fallback folders -->
    <clear />
  </fallbackPackageFolders>
</configuration>