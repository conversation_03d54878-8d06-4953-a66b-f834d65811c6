﻿using CsvHelper;
using System.Globalization;
using System.IO;

public class CsvDebugHelper
{
    public static void WriteTransactionsToCsv<T>(IEnumerable<T> transactions, string filePath)
    {
        using (var writer = new StreamWriter(filePath))
        using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
        {
            csv.WriteRecords(transactions);
            writer.Flush();  // Use the synchronous flush method
        }
    }
}