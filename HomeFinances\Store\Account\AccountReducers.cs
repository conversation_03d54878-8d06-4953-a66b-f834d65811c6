using Fluxor;

namespace HomeFinances.Store.Account;

public static class AccountReducers
{
    [ReducerMethod]
    public static AccountState ReduceSetCurrentAccountAction(AccountState state
        , SetCurrentAccountAction action)
    {
        // Ensure the selected account is valid (exists in AvailableAccounts)
        if (state.AvailableAccounts.Any(predicate: a => a.Id == action.AccountId))
            return new AccountState(currentAccountId: action.AccountId
                                    , availableAccounts: state.AvailableAccounts);
        // If the account is not valid, keep the current state
        return state;
    }
}