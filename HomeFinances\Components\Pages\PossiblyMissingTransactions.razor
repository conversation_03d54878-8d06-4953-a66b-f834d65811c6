﻿@page "/missing-transactions"
<hr/>
@* <MudButton OnClick="LoadData" Color="Color.Primary">Refresh Data through @DateTime.Now.AddDays(60).ToString("yyyy MMMM dd")</MudButton> *@
<hr />
@if (PossiblyMissingRecurringTransactions.Count > 0)
{
    <MudExpansionPanels>
        <MudExpansionPanel>
            <TitleContent>
                <div class="d-flex align-center">
                    <MudText Typo="Typo.h5">Possibly Missing Recurring Transactions (@PossiblyMissingRecurringTransactions.Count)</MudText>
                </div>
            </TitleContent>
            <ChildContent>
                @if (IsLoading)
                {
                    <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
                }
                else
                {
                    <MudTable Items="@PossiblyMissingRecurringTransactions" Striped="true" Hover="true" Bordered="true" Dense="true" FixedHeader="true">
                        <HeaderContent>
                            <MudTh><MudCheckBox T="bool" Color="Color.Primary" ValueChanged="SelectAllChanged" Value="@IsAllSelected" /></MudTh>
                            <MudTh>#</MudTh>
                            <MudTh>Description</MudTh>
                            <MudTh>Amount</MudTh>
                            <MudTh>Day</MudTh>
                            <MudTh>Ignore</MudTh>
                        </HeaderContent>
                        <RowTemplate>
                            <MudTd><MudCheckBox T="bool" Value="@context.IsChecked" ValueChanged="@(e => CheckBoxChanged(e, context))" /></MudTd>
                            <MudTd>@(context.DisplayIndex + 1)</MudTd>
                            <MudTd>@context.Description</MudTd>
                            <MudTd Style="@(context.Amount >= 0 ? "color: green;" : "color: red;")">@context.Amount.ToString("C")</MudTd>
                            <MudTd>@context.TransactionDate?.ToString("MMMM dd")</MudTd>
                            <MudTd>
                                <MudButton OnClick="@(() => IgnoreTransaction(context))" Color="Color.Secondary">Ignore</MudButton>
                            </MudTd>
                            </RowTemplate>
                    </MudTable>
                    <MudButton Class="mt-4" OnClick="AddSelectedToFutureTransactions" Color="Color.Success" Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.Add">
                        Add Selected to Future Transactions (@(PossiblyMissingRecurringTransactions?.Count(t => t.IsChecked) ?? 0))
                    </MudButton>
                }
            </ChildContent>
        </MudExpansionPanel>
    </MudExpansionPanels>
}
