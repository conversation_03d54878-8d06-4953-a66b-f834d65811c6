﻿using System.Globalization;
using CsvHelper;
using CsvHelper.Configuration;
using CsvHelper.TypeConversion;

namespace HomeFinances.Models.Mapping
{
    public class FlexibleDateTimeConverter : DefaultTypeConverter
    {
        public override object ConvertFromString(string text, IReaderRow row, MemberMapData memberMapData)
        {
            if (DateTime.TryParse(text, CultureInfo.InvariantCulture, DateTimeStyles.None, out var date))
            {
                return date;
            }

            throw new TypeConverterException(this, memberMapData, text, row.Context, "Cannot convert to DateTime.");
        }
    }
}
