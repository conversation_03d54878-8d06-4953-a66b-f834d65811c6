name: Build and deploy ASP.NET Core app to AWS Elastic Beanstalk

on:
  push:
    branches: [aws]
  workflow_dispatch:

# Added permissions block - REQUIRED for OIDC
permissions:
  id-token: write
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up .NET
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '8.0.x'

      - name: Restore dependencies
        run: dotnet restore HomeFinances/HomeFinances.csproj

      - name: Build and publish
        run: dotnet publish HomeFinances/HomeFinances.csproj --configuration Release --output publish

      - name: Upload artifact for deployment
        uses: actions/upload-artifact@v4   # use v4
        with:
          name: dotnet-app
          path: publish
    
  deploy:
    runs-on: ubuntu-latest
    needs: build
    environment:
      name: production
    # You could scope permissions here instead of top-level if preferred
    # permissions:
    #   id-token: write
    steps:
      - name: Download artifact
        uses: actions/download-artifact@v4   # use v4 (files go directly into 'publish')
        with:
          name: dotnet-app
          path: publish

      - name: Check Region Secret
        run: echo ${{ secrets.AWS_REGION }}
        
      - name: Check ARN Format 
        run: |
          echo "Checking ARN Format..." # Added an echo to confirm step runs
          if [[ "${{ secrets.AWS_ROLE_ARN }}" == arn:aws:iam::* ]]; then
            echo "ARN secret seems to start correctly."
          else
            echo "ARN secret does NOT look like a valid ARN: '${{ secrets.AWS_ROLE_ARN }}'"
            echo "Workflow will fail."
            exit 1
          fi

      - name: Configure AWS credentials via OIDC
        uses: aws-actions/configure-aws-credentials@v2
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_ARN }}
          aws-region: ${{ secrets.AWS_REGION }} 

      - name: Verify deployment variables
        run: bash .github/scripts/verify-secrets.sh

      - name: Zip published files
        run: |
          # Ensure the 'publish' directory exists from download-artifact@v4
          if [ -d "publish" ]; then
            cd publish
            zip -r ../app.zip .
            echo "Zipped contents of publish directory."
          else
            echo "Error: 'publish' directory not found after download."
            exit 1
          fi

      - name: Deploy to Elastic Beanstalk
        env:
          # Removed extra spaces after colons for consistency
          APPLICATION_NAME: ${{ secrets.EB_APPLICATION_NAME }}
          ENVIRONMENT_NAME: ${{ secrets.EB_ENVIRONMENT_NAME }}
          S3_BUCKET: ${{ secrets.EB_S3_BUCKET }}
          VERSION_LABEL: ${{ github.sha }}
        run: |
          echo "Starting deployment to Elastic Beanstalk..."
          aws s3 cp app.zip s3://$S3_BUCKET/$VERSION_LABEL.zip
          echo "Uploaded app.zip to s3://$S3_BUCKET/$VERSION_LABEL.zip"
          
          aws elasticbeanstalk create-application-version \
            --application-name "$APPLICATION_NAME" \
            --version-label "$VERSION_LABEL" \
            --source-bundle S3Bucket="$S3_BUCKET",S3Key="$VERSION_LABEL.zip"
          echo "Created application version $VERSION_LABEL"

          aws elasticbeanstalk update-environment \
            --environment-name "$ENVIRONMENT_NAME" \
            --version-label "$VERSION_LABEL"
          echo "Initiated environment update for $ENVIRONMENT_NAME to version $VERSION_LABEL"