﻿using HomeFinances.Models;

namespace HomeFinances.Store.RecurringTransactions
{
    public class RecurringTransactionActions
    {
        public record LoadRecurringTransactionsAction();
        public record LoadRecurringTransactionsSuccessAction(List<RecurringTransaction> Transactions);
        public record LoadRecurringTransactionsFailedAction(string ErrorMessage);

        public record AddOrUpdateRecurringTransactionAction(RecurringTransaction Transaction);
        public record AddOrUpdateRecurringTransactionSuccessAction(RecurringTransaction Transaction);
        public record AddOrUpdateRecurringTransactionFailedAction(string ErrorMessage);

        public record DeleteRecurringTransactionAction(Guid TransactionId);
        public record DeleteRecurringTransactionSuccessAction(Guid TransactionId);
        public record DeleteRecurringTransactionFailedAction(string ErrorMessage);
    }
}