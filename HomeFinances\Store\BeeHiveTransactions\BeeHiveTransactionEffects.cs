﻿using Fluxor;
using HomeFinances.Services;
using HomeFinances.Store.BankTransactions;
using static HomeFinances.Store.BeeHiveTransactions.BeeHiveTransactionActions;

namespace HomeFinances.Store.BeeHiveTransactions;

public class BeeHiveTransactionEffects
{
    private readonly CategoryService _categoryService;
    private readonly IFutureTransactionService _futureTransactionService;
    private readonly ILogger<BeeHiveTransactionEffects> _logger;
    private readonly TransactionReadWriteService _transactionReadWriteService;

    public BeeHiveTransactionEffects(TransactionReadWriteService transactionReadWriteService
        , CategoryService categoryService
        , IFutureTransactionService futureTransactionService
        , ILogger<BeeHiveTransactionEffects> logger)
    {
        _transactionReadWriteService = transactionReadWriteService;
        _categoryService = categoryService;
        _futureTransactionService = futureTransactionService;
        _logger = logger;

        _logger.LogWarning(
            message: "BeeHiveTransactionEffects constructor called - effect is being registered.");
    }

    [EffectMethod]
    public async Task HandleLoadBeehiveTransactionsAction(BeeHiveTransactionActions.LoadBeehiveTransactionsAction action
        , IDispatcher dispatcher)
    {
        Console.WriteLine("🚀 BeeHiveTransactionEffects.HandleLoadBeehiveTransactionsAction - EFFECT METHOD CALLED!");
        _logger.LogInformation(message: "🚀 BeeHiveTransactionEffects.HandleLoadBeehiveTransactionsAction - EFFECT METHOD CALLED!");
        _logger.LogInformation(message: "🔥 HandleLoadBeehiveTransactionsAction effect method entered.");
        try
        {
            _logger.LogInformation(message: "Starting to load BeehiveTransactions from AWS S3");
            var transactions = await _transactionReadWriteService.ReadBeehiveTransactionsFromS3();

            _logger.LogInformation(message: "Loaded {TransactionCount} BeehiveTransactions from S3"
                                   , transactions.Count());

            foreach (var transaction in transactions)
            {
                //transaction.Taxonomy = await _categoryMappingService.GetCategoryAndSubCategory(transaction.Description);
            }

            dispatcher.Dispatch(action: new BeeHiveTransactionActions.LoadBeehiveTransactionsSuccessAction(Transactions: transactions));
            dispatcher.Dispatch(
                action: new BankTransactionActions.MergeBeehiveTransactionsAction(BeeHiveTransactions: transactions));

            _logger.LogInformation(
                message: "Successfully dispatched LoadBeehiveTransactionsSuccessAction and MergeBeehiveTransactionsAction");
        }
        catch (Exception ex)
        {
            _logger.LogError(exception: ex
                             , message: "Failed to load BeehiveTransactions from S3");
            dispatcher.Dispatch(action: new BeeHiveTransactionActions.LoadBeehiveTransactionsFailedAction(ErrorMessage: ex.Message));
        }
    }
}