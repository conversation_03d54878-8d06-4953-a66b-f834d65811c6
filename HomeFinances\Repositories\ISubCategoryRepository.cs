using CSharpFunctionalExtensions;

namespace HomeFinances.Repositories;

/// <summary>
///     Repository abstraction for subcategories.
/// </summary>
public interface ISubCategoryRepository
{
    /// <summary>
    ///     Gets the list of subcategories for the specified category asynchronously.
    /// </summary>
    /// <param name="category">The category name.</param>
    Task<Result<List<string>>> GetSubCategoriesAsync(string category); // Changed IList to List for compliance

    /// <summary>
    ///     Adds the specified subcategory to the given category asynchronously.
    /// </summary>
    /// <param name="category">The category name.</param>
    /// <param name="subCategory">The subcategory to add.</param>
    Task<Result> AddSubCategoryAsync(string category
        , string subCategory);

    /// <summary>
    ///     Removes the specified subcategory from the given category asynchronously.
    ///     Returns true if the subcategory was removed; false otherwise.
    /// </summary>
    /// <param name="category">The category name.</param>
    /// <param name="subCategory">The subcategory to remove.</param>
    Task<Result<bool>> RemoveSubCategoryAsync(string category
        , string subCategory);
}