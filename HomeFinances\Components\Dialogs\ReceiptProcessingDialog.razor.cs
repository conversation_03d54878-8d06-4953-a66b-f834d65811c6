using HomeFinances.Models;
using HomeFinances.Services;
using Microsoft.AspNetCore.Components;
using MudBlazor;

namespace HomeFinances.Components.Dialogs
{
    public partial class ReceiptProcessingDialog
    {
        [CascadingParameter] MudDialogInstance MudDialog { get; set; } = default!;
        [Parameter] public FutureBankTransaction Transaction { get; set; } = default!;
        [Parameter] public string ReceiptPath { get; set; } = default!;

        [Inject] private CategoryService CategoryService { get; set; } = default!;
        [Inject] private ILogger<ReceiptProcessingDialog> Logger { get; set; } = default!;

        private MudForm form = default!;
        private bool isLoading = true;

        protected override async Task OnInitializedAsync()
        {
            await base.OnInitializedAsync();
            Logger.LogInformation("ReceiptProcessingDialog initialized with Transaction: {@Transaction}", Transaction);
            Logger.LogInformation("Transaction Amount: {Amount}", Transaction.TransactionAmount);
            Logger.LogInformation("Taxonomy Amount: {Amount}", Transaction.Taxonomy.Amount);
            
            // Set the transaction amount from taxonomy if not already set
            if (Transaction.TransactionAmount == 0 && Transaction.Taxonomy.Amount > 0)
            {
                Transaction.TransactionAmount = Transaction.Taxonomy.Amount;
                Logger.LogInformation("Set transaction amount from taxonomy: {Amount}", Transaction.TransactionAmount);
            }
            
            // Simulate a small delay to show the loading indicator
            await Task.Delay(500);
            isLoading = false;
        }

        private string GetImageUrl()
        {
            // Convert the file path to a relative URL
            var fileName = Path.GetFileName(ReceiptPath);
            return $"/Data/Uploads/Receipts/{fileName}";
        }

        private void Cancel()
        {
            MudDialog.Cancel();
        }

        private async Task Submit()
        {
            await form.Validate();
            if (form.IsValid)
            {
                Logger.LogInformation("Submitting transaction with amount: {Amount}", Transaction.TransactionAmount);
                // Ensure amount is negative for expenses
                if (Transaction.TransactionAmount > 0)
                {
                    Transaction.TransactionAmount = -Transaction.TransactionAmount;
                    Logger.LogInformation("Converted amount to negative: {Amount}", Transaction.TransactionAmount);
                }
                MudDialog.Close(DialogResult.Ok(Transaction));
            }
        }
    }
} 