using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Amazon.S3;
using Amazon.S3.Model;
using CsvHelper;
using CsvHelper.Configuration;
using HomeFinances.Models;
using HomeFinances.Models.Mapping;
using Microsoft.Extensions.Logging;
using CSharpFunctionalExtensions;

namespace HomeFinances.Repositories
{
    /// <summary>
    /// AWS S3-based implementation of IBankTransactionRepository using CSV files stored in S3.
    /// </summary>
    public class AwsS3BankTransactionRepository : IBankTransactionRepository
    {
        private readonly IAmazonS3 _s3Client;
        private readonly ILogger<AwsS3BankTransactionRepository> _logger;
        private readonly CsvConfiguration _csvConfig;
        private readonly string _bucketName;

        public AwsS3BankTransactionRepository(IAmazonS3 s3Client, ILogger<AwsS3BankTransactionRepository> logger)
        {
            _s3Client = s3Client ?? throw new ArgumentNullException(nameof(s3Client), "S3 client cannot be null. AWS backend is required.");
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // Get bucket name from environment variable
            var connectionJson = Environment.GetEnvironmentVariable("AWS_S3_HOMEFINANCE_CONNECTION");
            if (!string.IsNullOrEmpty(connectionJson))
            {
                try
                {
                    var connectionSettings = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(connectionJson);
                    if (connectionSettings != null && connectionSettings.TryGetValue("BucketName", out var bucketName))
                    {
                        _bucketName = bucketName;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to parse AWS_S3_HOMEFINANCE_CONNECTION JSON");
                    throw new InvalidOperationException("Failed to parse AWS connection settings", ex);
                }
            }

            if (string.IsNullOrEmpty(_bucketName))
            {
                _bucketName = "home-finances";
                _logger.LogWarning("Using default bucket name: {BucketName}", _bucketName);
            }

            _csvConfig = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true
            };
        }

        /// <summary>
        /// Verifies that the S3 bucket exists and is accessible
        /// </summary>
        private async Task<bool> VerifyBucketExistsAsync()
        {
            try
            {
                var response = await _s3Client.ListBucketsAsync().ConfigureAwait(false);
                var bucketExists = response.Buckets.Any(b => b.BucketName == _bucketName);

                if (!bucketExists)
                {
                    _logger.LogWarning("S3 bucket {BucketName} does not exist", _bucketName);
                    throw new InvalidOperationException($"S3 bucket {_bucketName} does not exist. Please create it before using this repository.");
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying S3 bucket {BucketName}", _bucketName);
                throw new InvalidOperationException($"Error accessing S3 bucket {_bucketName}. AWS backend is required.", ex);
            }
        }

        public async Task<Result<List<BankTransaction>>> ReadTransactionsAsync(string filePath)
        {
            try
            {
                // Verify bucket exists before attempting operations
                await VerifyBucketExistsAsync().ConfigureAwait(false);

                // Convert local file path to S3 key
                var s3Key = Path.GetFileName(filePath);

                try
                {
                    var request = new GetObjectRequest
                    {
                        BucketName = _bucketName,
                        Key = s3Key
                    };

                    var response = await _s3Client.GetObjectAsync(request).ConfigureAwait(false);

                    var records = new List<BankTransaction>();

                    using (var reader = new StreamReader(response.ResponseStream))
                    using (var csv = new CsvReader(reader, _csvConfig))
                    {
                        csv.Context.RegisterClassMap<BankTransactionMap>();
                        records = csv.GetRecords<BankTransaction>().ToList();
                    }

                    _logger.LogInformation("Successfully read {Count} transactions from S3 bucket {BucketName}, key {Key}",
                        records.Count, _bucketName, s3Key);

                    return Result.Success<List<BankTransaction>>(records);
                }
                catch (AmazonS3Exception s3Ex) when (s3Ex.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    _logger.LogWarning("File not found in S3 bucket {BucketName}, key {Key}. Creating empty file.",
                        _bucketName, s3Key);

                    // Create an empty file in S3
                    await WriteTransactionsAsync(new List<BankTransaction>(), filePath).ConfigureAwait(false);

                    return Result.Success<List<BankTransaction>>(new List<BankTransaction>());
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading transactions from S3.");
                return Result.Failure<List<BankTransaction>>("Failed to read transactions from AWS S3. AWS backend is required.");
            }
        }

        public async Task<Result> WriteTransactionsAsync(IEnumerable<BankTransaction> transactions, string filePath)
        {
            try
            {
                // Verify bucket exists before attempting operations
                await VerifyBucketExistsAsync().ConfigureAwait(false);

                // Convert local file path to S3 key
                var s3Key = Path.GetFileName(filePath);

                using (var memoryStream = new MemoryStream())
                {
                    using (var writer = new StreamWriter(memoryStream, Encoding.UTF8, leaveOpen: true))
                    using (var csv = new CsvWriter(writer, _csvConfig))
                    {
                        csv.Context.RegisterClassMap<BankTransactionMap>();
                        csv.WriteRecords(transactions);
                    }

                    memoryStream.Position = 0;

                    var putRequest = new PutObjectRequest
                    {
                        BucketName = _bucketName,
                        Key = s3Key,
                        InputStream = memoryStream,
                        ContentType = "text/csv"
                    };

                    await _s3Client.PutObjectAsync(putRequest).ConfigureAwait(false);
                }

                _logger.LogInformation("Successfully wrote transactions to S3 bucket {BucketName}, key {Key}",
                    _bucketName, s3Key);
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error writing transactions to S3.");
                return Result.Failure("Failed to write transactions to AWS S3. AWS backend is required.");
            }
        }
    }
}