@using HomeFinances.Models
@using HomeFinances.Services
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudContainer Style="max-height: 80vh; overflow-y: auto">
            <MudGrid>
                <MudItem xs="6">
                    <MudPaper Class="pa-4" Style="height: 100%">
                        <MudText Typo="Typo.h6" Class="mb-4">Receipt Image</MudText>
                        @if (isLoading)
                        {
                            <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
                        }
                        else
                        {
                            <MudImage Src="@GetImageUrl()" Alt="Receipt" Style="max-width: 100%; max-height: 60vh;" ObjectFit="ObjectFit.Contain" />
                        }
                    </MudPaper>
                </MudItem>
                <MudItem xs="6">
                    <MudPaper Class="pa-4">
                        <MudText Typo="Typo.h6" Class="mb-4">Transaction Details</MudText>
                        <MudForm Model="@Transaction" @ref="form">
                            <MudNumericField @bind-Value="Transaction.TransactionAmount" 
                                           Label="Amount" 
                                           Required="true"
                                           Format="N2"
                                           AdornmentIcon="@Icons.Material.Filled.AttachMoney"
                                           AdornmentText="$"
                                           Class="mb-4" />

                            <MudDatePicker @bind-Date="Transaction.TransactionDate" 
                                         Label="Date"
                                         Required="true"
                                         Class="mb-4" />

                            <MudSelect T="string" 
                                     @bind-Value="Transaction.Taxonomy.Category" 
                                     Label="Category"
                                     Class="mb-4">
                                <MudSelectItem T="string" Value="@null">Select Category</MudSelectItem>
                                @foreach (var category in CategoryService.GetAllCategoriesAndSubcategories())
                                {
                                    <MudSelectItem T="string" Value="@category">@category</MudSelectItem>
                                }
                            </MudSelect>

                            <MudSelect T="string" 
                                     @bind-Value="Transaction.Taxonomy.SubCategory" 
                                     Label="Subcategory"
                                     Disabled="@(string.IsNullOrEmpty(Transaction.Taxonomy.Category))"
                                     Class="mb-4">
                                <MudSelectItem T="string" Value="@null">Select Subcategory</MudSelectItem>
                                @if (!string.IsNullOrEmpty(Transaction.Taxonomy.Category))
                                {
                                    @foreach (var subCategory in CategoryService.GetSubCategories(Transaction.Taxonomy.Category))
                                    {
                                        <MudSelectItem T="string" Value="@subCategory">@subCategory</MudSelectItem>
                                    }
                                }
                            </MudSelect>

                            <MudTextField @bind-Value="Transaction.Description" 
                                        Label="Description" 
                                        Required="true"
                                        Lines="5"
                                        Class="mb-4" />
                        </MudForm>
                    </MudPaper>
                </MudItem>
            </MudGrid>
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">Cancel</MudButton>
        <MudButton Color="Color.Primary" OnClick="Submit">Save</MudButton>
    </DialogActions>
</MudDialog> 