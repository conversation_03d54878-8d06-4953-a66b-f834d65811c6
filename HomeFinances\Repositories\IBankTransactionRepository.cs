using System.Collections.Generic;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using HomeFinances.Models;

namespace HomeFinances.Repositories
{
    /// <summary>
    /// Repository abstraction for bank transaction storage.
    /// </summary>
    public interface IBankTransactionRepository
    {
        /// <summary>
        /// Reads bank transactions from the specified data source.
        /// </summary>
        Task<Result<List<BankTransaction>>> ReadTransactionsAsync(string filePath);

        /// <summary>
        /// Writes bank transactions to the specified data source.
        /// </summary>
        Task<Result> WriteTransactionsAsync(IEnumerable<BankTransaction> transactions, string filePath);
    }
}