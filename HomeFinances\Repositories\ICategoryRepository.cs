using CSharpFunctionalExtensions;

namespace HomeFinances.Repositories;

/// <summary>
///     Repository abstraction for categories.
/// </summary>
public interface ICategoryRepository : ISubCategoryRepository
{
    /// <summary>
    ///     Gets all categories asynchronously.
    /// </summary>
    Task<Result<List<string>>> GetAllCategoriesAsync(); // Changed IList to List for compliance

    /// <summary>
    ///     Adds a new category asynchronously.
    /// </summary>
    /// <param name="category">The category name.</param>
    Task<Result> AddCategoryAsync(string category);
}