﻿using Fluxor;
using static HomeFinances.Store.GeneratedImages.GeneratedImagesActions;

namespace HomeFinances.Store.GeneratedImages
{
    public class GeneratedImagesReducers
    {
        [ReducerMethod]
        public static GeneratedImagesState ReduceLoadGeneratedImagesAction(GeneratedImagesState state, LoadGeneratedImagesAction action) =>
            state with { IsLoading = true, ErrorMessage = null };

        [ReducerMethod]
        public static GeneratedImagesState ReduceLoadGeneratedImagesSuccessAction(GeneratedImagesState state, LoadGeneratedImagesSuccessAction action) =>
            state with { IsLoading = false, Images = action.Images.ToList() };

        [ReducerMethod]
        public static GeneratedImagesState ReduceLoadGeneratedImagesFailureAction(GeneratedImagesState state, LoadGeneratedImagesFailureAction action) =>
            state with { IsLoading = false, ErrorMessage = action.ErrorMessage };

        [ReducerMethod]
        public static GeneratedImagesState ReduceAddGeneratedImageAction(GeneratedImagesState state, AddGeneratedImageAction action) =>
            state with { IsLoading = true, ErrorMessage = null };

        [ReducerMethod]
        public static GeneratedImagesState ReduceAddGeneratedImageSuccessAction(GeneratedImagesState state, AddGeneratedImageSuccessAction action) =>
            state with { IsLoading = false, Images = state.Images.Append(action.Image).ToList() };

        [ReducerMethod]
        public static GeneratedImagesState ReduceAddGeneratedImageFailureAction(GeneratedImagesState state, AddGeneratedImageFailureAction action) =>
            state with { IsLoading = false, ErrorMessage = action.ErrorMessage };

        [ReducerMethod]
        public static GeneratedImagesState ReduceSetRegeneratingImageAction(GeneratedImagesState state, SetRegeneratingImageAction action)
        {
            var regeneratingImages = new Dictionary<string, bool>(state.RegeneratingImages)
            {
                [action.Prompt] = action.IsRegenerating
            };

            return state with { RegeneratingImages = regeneratingImages };
        }

        [ReducerMethod]
        public static GeneratedImagesState ReduceSetRegeneratingImageSuccessAction(GeneratedImagesState state, SetRegeneratingImageSuccessAction action)
        {
            var updatedImages = state.Images.Select(img => img.Prompt == action.Prompt ? action.image : img).ToList();
            var regeneratingImages = new Dictionary<string, bool>(state.RegeneratingImages)
            {
                [action.Prompt] = false  
            };

            return state with { Images = updatedImages, RegeneratingImages = regeneratingImages };
        }

        [ReducerMethod]
        public static GeneratedImagesState ReduceSetRegeneratingImageFailureAction(GeneratedImagesState state, SetRegeneratingImageFailureAction action)
        {
            var regeneratingImages = new Dictionary<string, bool>(state.RegeneratingImages)
            {
                [action.Prompt] = false
            };

            return state with { ErrorMessage = action.ErrorMessage, RegeneratingImages = regeneratingImages };
        }

        [ReducerMethod]
        public static GeneratedImagesState ReduceDeleteGeneratedImageAction(GeneratedImagesState state, DeleteGeneratedImageAction action) =>
            state with { IsLoading = true, ErrorMessage = null };

        [ReducerMethod]
        public static GeneratedImagesState ReduceDeleteGeneratedImageSuccessAction(GeneratedImagesState state, DeleteGeneratedImageSuccessAction action)
        {
            var updatedImages = state.Images.Where(img => !(img.PartitionKey == action.PartitionKey && img.Prompt == action.RowKey)).ToList();
            return state with { IsLoading = false, Images = updatedImages };
        }

        [ReducerMethod]
        public static GeneratedImagesState ReduceDeleteGeneratedImageFailureAction(GeneratedImagesState state, DeleteGeneratedImageFailureAction action) =>
            state with { IsLoading = false, ErrorMessage = action.ErrorMessage };

        [ReducerMethod]
        public static GeneratedImagesState ReduceUpdateGeneratedImageAction(GeneratedImagesState state, UpdateGeneratedImageAction action) =>
            state with { IsLoading = true, ErrorMessage = null };

        [ReducerMethod]
        public static GeneratedImagesState ReduceUpdateGeneratedImageSuccessAction(GeneratedImagesState state, UpdateGeneratedImageSuccessAction action)
        {
            var updatedImages = state.Images.Select(img => img.Prompt == action.Image.Prompt ? action.Image : img).ToList();
            return state with { IsLoading = false, Images = updatedImages };
        }

        [ReducerMethod]
        public static GeneratedImagesState ReduceUpdateGeneratedImageFailureAction(GeneratedImagesState state, UpdateGeneratedImageFailureAction action) =>
            state with { IsLoading = false, ErrorMessage = action.ErrorMessage };
    }
}
