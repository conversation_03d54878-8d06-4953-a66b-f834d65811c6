namespace HomeFinances.Repositories;

/// <summary>
///     Repository abstraction for recurring transactions.
/// </summary>
public interface IRecurringTransactionRepository
{
    /// <summary>
    ///     Gets all recurring transactions.
    /// </summary>
    Task<IEnumerable<RecurringTransaction>> GetAllAsync();

    /// <summary>
    ///     Save all recurring transactions.
    /// </summary>
    Task<IEnumerable<RecurringTransaction>> SaveListAsync(List<RecurringTransaction> transactions);

    /// <summary>
    ///     Gets a recurring transaction by its identifier.
    /// </summary>
    Task<RecurringTransaction> GetByIdAsync(Guid id);

    /// <summary>
    ///     Adds or updates a recurring transaction.
    /// </summary>
    Task AddOrUpdateAsync(RecurringTransaction transaction);

    /// <summary>
    ///     Deletes a recurring transaction by its identifier.
    /// </summary>
    Task DeleteAsync(Guid id);

    /// <summary>
    ///     Saves all recurring transactions, typically overwriting the existing set.
    /// </summary>
    Task SaveAllAsync(List<RecurringTransaction> transactions);
}