@page "/recurring-transactions"
@rendermode InteractiveServer
@using HomeFinances.Models
@using HomeFinances.Components.Dialogs
@using HomeFinances.Store.RecurringTransactions
@using Fluxor

<MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="pa-0">
    <div class="main-content">
        <MudStack Row="true" Spacing="2" Class="mb-4">
            <MudButton Variant="Variant.Filled"
                      Color="Color.Primary"
                      StartIcon="@Icons.Material.Filled.Add"
                      OnClick="OpenAddTransactionDialog">
                Add Recurring Transaction
            </MudButton>
        </MudStack>
        <MudStack Spacing="2">
            <MudExpansionPanels>
    <MudExpansionPanel>
        <TitleContent>
            <div class="d-flex align-center">
                <MudText Typo="Typo.h5">Recurring Transactions (@(RecurringTransactionsState.Value.RecurringTransactions.Count()))</MudText>
                @if (RecurringTransactionsState.Value.Loading.Status != LoadingStatusEnum.Loaded)
                {
                    <MudProgressCircular Size="Size.Small" Color="Color.Primary" Indeterminate="true" Class="ml-2" />
                }
            </div>
        </TitleContent>
        <ChildContent>
            @if (RecurringTransactionsState.Value.Loading.Status == LoadingStatusEnum.Error)
            {
                <MudText Color="Color.Error">An error occurred while loading transactions.</MudText>
            }
            else if (RecurringTransactionsState.Value.Loading.Status != LoadingStatusEnum.Loaded)
            {
                <MudProgressCircular Indeterminate="true" />
            }
            else
            {
                <MudStack>
                    <MudTable Items="@_recurringTransactions" Dense="true" Hover="true" Breakpoint="Breakpoint.Sm">
                        <HeaderContent>
                            <MudTh>Description</MudTh>
                            <MudTh>Day of Month</MudTh>
                            <MudTh>Amount</MudTh>
                            <MudTh>End Date</MudTh>
                            <MudTh>Outgo</MudTh>
                            <MudTh></MudTh>
                        </HeaderContent>
                        <RowTemplate>
                            <MudTd DataLabel="Description">@context.Description</MudTd>
                            <MudTd DataLabel="Day of Month">@context.DayOfMonth</MudTd>
                            <MudTd DataLabel="Amount">@context.Amount.ToString("C")</MudTd>
                            <MudTd DataLabel="End Date">@(context.EndDate == default ? "Never" : context.EndDate.ToShortDateString())</MudTd>
                            <MudTd DataLabel="Outgo">@context.IsOutgo</MudTd>
                            <MudTd>
                                <MudIconButton Icon="@Icons.Material.Filled.Delete" Color="Color.Error" OnClick="@(() => DeleteTransaction(context))" />
                            </MudTd>
                        </RowTemplate>
                    </MudTable>
                    
                </MudStack>
            }
        </ChildContent>
    </MudExpansionPanel>
</MudExpansionPanels>
        </MudStack>
    </div>
</MudContainer>

<style>
    .main-content {
        padding: 1rem;
        background-color: #f0f8ff;
        min-height: 100vh;
        width: 100%;
    }

    ::deep .mud-expansion-panel {
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        margin-bottom: 0.5rem;
        background-color: white;
        width: 100%;
    }

    ::deep .mud-expand-panel-header {
        background-color: #fafafa;
    }

    ::deep .mud-stack {
        width: 100%;
    }
</style>