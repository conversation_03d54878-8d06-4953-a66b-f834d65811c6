﻿@using Fluxor
@using HomeFinances.Models
@using HomeFinances.Services
@using HomeFinances.Store.Account
@using HomeFinances.Store.BankTransactions
@using HomeFinances.Store.FutureTransactions
@using HomeFinances.Store.RecurringTransactions

@inject IState<BankTransactionState> BankTransactionState
@inject IDispatcher Dispatcher
@inject ILogger<PossibleMatch> _logger
@rendermode InteractiveServer

<style>
    .matches-header {
        font-size: 1.2em;
        color: #555;
        margin-bottom: 10px;
    }

    .matches-container {
        display: flex;
        flex-direction: column;
        gap: 10px; /* Spacing between transactions */
    }

    .transaction {
        display: flex;
        justify-content: space-between;
        padding: 10px;
        background-color: #f3f3f3;
        border-radius: 8px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .transaction span {
        flex: 1;
        padding: 0 10px; /* Padding around text for better readability */
    }

    .transaction span:nth-child(1) {
        flex: 0.5;
    }

    .transaction span:nth-child(3) {
        color: green;
        text-align: right;
    }

</style>
@if (PossibleMatches?.Any() ?? false)
{
    <MudButton OnClick="@ToggleTheMatches" Variant="Variant.Filled" Color="Color.Primary">Possible Matches (@PossibleMatches.Count())</MudButton>
    @if (showMatches)
    {
        <div class="matches-container">
            @foreach (var transaction in PossibleMatches)
            {
                <div class="transaction">
                    <span>@transaction.TransactionDate?.ToShortDateString()</span>
                    <span>@transaction.Description</span>
                    <span>@transaction.TransactionAmount.ToString("C")</span>
                    <span> 
                        <MudButton OnClick="@(() => MatchTransactionAsync(transaction))"
                                   Color="Color.Warning">Match</MudButton>
                    </span>

                </div>
            }
        </div>
    }
}

@code {

    [Parameter] public FutureBankTransaction FutureTransaction { get; set; }
    private List<BankTransaction> PossibleMatches =>
        BankTransactionState?.Value?.Transactions?
            .Where(x => IsPossibleMatch(x, FutureTransaction))
            .DistinctBy(x=>x.BankCode)
            .ToList() ?? new List<BankTransaction>();

    private bool showMatches = false;

    protected override void OnInitialized()
    {
        BankTransactionState.StateChanged += OnStateHasChanged;
    }
    private bool IsPossibleMatch(BankTransaction transaction, FutureBankTransaction futureTransaction)
    {
        var isMatch = transaction.TransactionAmount == futureTransaction.TransactionAmount
               && futureTransaction.TransactionDate.HasValue
               && Math.Abs((transaction.TransactionDate - futureTransaction.TransactionDate.Value)?.TotalDays ?? 0) <= 14;

        return isMatch;
    }
    
    private void OnStateHasChanged(object? sender, EventArgs e)
    {
        StateHasChanged();
    }
    public void Dispose()
    {
        BankTransactionState.StateChanged -= OnStateHasChanged;
    }
    void ToggleTheMatches()
    {
        showMatches = !showMatches;
    }
    private async Task MatchTransactionAsync(BankTransaction transactionToMatch)
    {
        if (transactionToMatch == null || FutureTransaction == null) return;

        _logger.LogInformation(
            message:
            $"Attempting to match FutureTransaction ID: {FutureTransaction.Id} with BankTransaction: {transactionToMatch.Description} - {transactionToMatch.TransactionAmount:C} on {transactionToMatch.TransactionDate?.ToShortDateString()}");
        Dispatcher.Dispatch(new FutureTransactionActions.DeleteFutureTransactionAction(FutureTransaction));
    }
}