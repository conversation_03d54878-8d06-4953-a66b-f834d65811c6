﻿using Fluxor;
using HomeFinances.Models;

namespace HomeFinances.Store.IgnoredTransactions
{
    [FeatureState]
    public record IgnoredTransactionsState
    {
        public List<PossiblyMissingTransactionViewModel> IgnoredTransactions { get; set; } = new List<PossiblyMissingTransactionViewModel>();
        public LoadingState Loading { get; set; } = new LoadingState { Status = LoadingStatusEnum.NotLoaded };
    }
}