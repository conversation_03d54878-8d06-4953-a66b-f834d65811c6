﻿using System.Text.Json.Serialization;

namespace HomeFinances.Models.ChatGPT
{
    public class ImageModelRequestData 
    {

        [JsonPropertyName("model")]
        public string Model { get; set; }

        [JsonPropertyName("messages")]
        public List<ComplexMessage> Messages { get; set; }

        [JsonPropertyName("temperature")]
        public double Temperature { get; set; }

        [JsonPropertyName("max_tokens")]
        public int MaxTokens { get; set; }

    }
}