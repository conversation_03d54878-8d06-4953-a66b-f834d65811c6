using Fluxor;

namespace HomeFinances.Store.Downloads
{
    [FeatureState]
    public record DownloadsState
    {
        public List<string> FilePaths { get; init; }
        public bool IsLoading { get; init; }
        public string ErrorMessage { get; init; }

        public DownloadsState()
        {
            FilePaths = new List<string>();
            IsLoading = false;
            ErrorMessage = string.Empty;
        }
    }
} 