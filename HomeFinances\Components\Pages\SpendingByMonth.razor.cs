﻿using Fluxor;
using HomeFinances.Models;
using HomeFinances.Store.BankTransactions;
using Microsoft.AspNetCore.Components;

namespace HomeFinances.Components.Pages
{
    public partial class SpendingByMonth
    {
        [Inject] IState<BankTransactionState> TransactionState { get; set; }
        [Inject] private IDispatcher Dispatcher { get; set; }

        private Dictionary<(int Year, int Month), List<BankTransaction>> transactionsByMonth;

        protected override void OnInitialized()
        {
            base.OnInitialized();
            TransactionState.StateChanged += OnTransactionStateChanged;
            if (TransactionState.Value.Loading.Status == LoadingStatusEnum.NotLoaded)
            {
                Dispatcher.Dispatch(new BankTransactionActions.LoadTransactionsAction());
            }
            ProcessTransactions(); // Initial processing
        }

        private void OnTransactionStateChanged(object? sender, EventArgs e)
        {
            ProcessTransactions();
            StateHasChanged(); // Request a UI update
        }
        private void ProcessTransactions()
        {
            if (TransactionState.Value.Loading.Status != LoadingStatusEnum.Loaded)
            {
                transactionsByMonth = new Dictionary<(int Year, int Month), List<BankTransaction>>();
                return;
            }

            var transactions = TransactionState.Value.Transactions ?? Enumerable.Empty<BankTransaction>();
            transactionsByMonth = transactions
                .GroupBy(t => (t.TransactionDate?.Year ?? 0, t.TransactionDate?.Month ?? 0))
                .ToDictionary(g => g.Key, g => g.ToList());
        }

        public void Dispose()
        {
            TransactionState.StateChanged -= OnTransactionStateChanged;
        }


        private decimal CalculateTotal(IEnumerable<BankTransaction> transactions, bool isIncome) => transactions
        .Where(t => isIncome ? t.TransactionAmount > 0 : t.TransactionAmount < 0)
        .Sum(t => t.TransactionAmount);

        private Dictionary<string, List<BankTransaction>> GroupTransactionsByCategory(IEnumerable<BankTransaction> transactions) => transactions
        .GroupBy(t => t.Taxonomy.Category)
    .ToDictionary(g => g.Key, g => g.ToList());
    }
}
