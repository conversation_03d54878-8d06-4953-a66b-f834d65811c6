﻿@page "/future-transactions"
@using Fluxor
@using HomeFinances.Store
@using HomeFinances.Store.BeeHiveTransactions
@using Dispatcher = Microsoft.AspNetCore.Components.Dispatcher
@rendermode InteractiveServer
@using HomeFinances.Models

<MudExpansionPanels>
    <MudExpansionPanel>
        <TitleContent>
            <div class="d-flex align-center">
                <MudText Typo="Typo.h5">Future Transactions (@futureTransactions.Count())</MudText>
                @if (FutureTransactionState.Value.Loading.Status != LoadingStatusEnum.Loaded)
                {
                    <MudProgressCircular Size="Size.Small" Color="Color.Primary" Indeterminate="true" Class="ml-2" />
                }
            </div>
        </TitleContent>
        <ChildContent>
            @if (FutureTransactionState.Value.Loading.Status == LoadingStatusEnum.Error)
            {
                <MudAlert Severity="Severity.Error">@FutureTransactionState.Value.Loading.ErrorMessage</MudAlert>
            }
            else if (FutureTransactionState.Value.Loading.Status != LoadingStatusEnum.Loaded)
            {
                <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
            }
            else
            {
                <MudStack Row="true" Class="mb-4">
                <MudButton Variant="Variant.Filled" Color="Color.Error" Disabled="@isDeleting" OnClick="@(() => ShowClearAllDialog())">Clear All Future Transactions</MudButton>
                <MudButton Variant="Variant.Filled"
                           Color="Color.Error"
                           Disabled="@(!selectedTransactions.Any() || isDeleting)"
                           OnClick="@(async _ => await DeleteSelected())">Delete Selected</MudButton>
                @if (isDeleting)
                {
                    <MudProgressCircular Indeterminate="true" Size="Size.Small" Class="ml-2 align-self-center" />
                }
                <MudButton Variant="Variant.Filled" Color="Color.Secondary" Disabled="@isDeleting" OnClick="@(() => ShowAddTransactionDialog())">Add Future Transaction</MudButton>
            </MudStack>
            <div class="d-flex justify-end mb-4">
                @{
                    var latestTransaction = LatestTransaction;
                    var balance = latestTransaction?.Balance ?? 0m;
                    var date = latestTransaction?.TransactionDate?.ToString("MM/dd/yyyy") ?? "N/A";
                }
                <MudText Typo="Typo.h6" Class="font-weight-bold">Current Bank Balance as of @date: @balance.ToString("C")</MudText>
            </div>
            @if (windowWidth > 600)
            {
                <MudTable Items="@futureTransactions" Striped="true" Hover="true" Bordered="true" Dense="true" FixedHeader="true" Editable="true">
                    <HeaderContent>
                        <MudTh>
                            <MudCheckBox T="bool"
                                         Checked="@(IsAllSelected)"
                                         CheckedChanged="@ToggleSelectAll"
                                         Indeterminate="@(IsIndeterminate)"
                                         DisableRipple="true" />
                        </MudTh>
                        <MudTh></MudTh>
                        <MudTh>Included</MudTh>
                        <MudTh>Date</MudTh>
                        <MudTh>Description</MudTh>
                        <MudTh>Amount</MudTh>
                        <MudTh>Category</MudTh>
                        <MudTh>Sub Category</MudTh>
                        <MudTh>Projected Balance</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd>
                            <MudCheckBox T="bool"
                                         Checked="@(selectedTransactions.Contains(context))"
                                         CheckedChanged="@((value) => OnRowChecked(context, value))"
                                         DisableRipple="true" />
                        </MudTd>
                        <MudTd DataLabel="DeleteRow">
                            <MudIconButton Style="padding: 0px;" Icon="@Icons.Material.Filled.Delete" OnClick="@(() => DeleteItem(@context))" Color="Color.Error"></MudIconButton>
                        </MudTd>
                        <MudTd DataLabel="DisableRow">
                            <MudIconButton Style="padding: 0px;" Icon="@GetEnabledIcon(context)" OnClick="@(() => ToggleEnabled(@context))" Color="Color.Error"></MudIconButton>
                        </MudTd>
                        <MudTd DataLabel="Date">@((context.TransactionDate == null ? "" : context.TransactionDate.ToString()))</MudTd>
                        <MudTd DataLabel="Description">@context.Description <PossibleMatch FutureTransaction="@context"></PossibleMatch></MudTd>
                        <MudTd DataLabel="Amount" Style="@(context.TransactionAmount >= 0 ? "color: green;" : "color: red;")">@context.TransactionAmount.ToString("C")</MudTd>
                        <MudTd DataLabel="Category">@context.Taxonomy?.Category</MudTd>
                        <MudTd DataLabel="SubCategory">@context.Taxonomy?.SubCategory</MudTd>
                        <MudTd DataLabel="Projected Balance" Style="@(context.ProjectedBalance >= 0 ? "color: green;" : "color: red;")">@context.ProjectedBalance.ToString("C")</MudTd>
                    </RowTemplate>
                    <RowEditingTemplate>
                        <MudTd>
                            <MudCheckBox T="bool"
                                         Checked="@(selectedTransactions.Contains(context))"
                                         CheckedChanged="@(value => { OnRowChecked(context, value); })"
                                         DisableRipple="true" />
                        </MudTd>
                        <MudTd DataLabel="DeleteRow">
                            <MudIconButton Icon="@Icons.Material.Filled.Delete" OnClick="@(() => DeleteItem(@context))" Color="Color.Error"></MudIconButton>
                        </MudTd>
                        <MudTd DataLabel="DisableRow">
                            <MudIconButton Icon="@GetEnabledIcon(context)" OnClick="@(() => ToggleEnabled(@context))" Color="Color.Error"></MudIconButton>
                        </MudTd>
                        <MudTd DataLabel="Date">
                            <MudDatePicker DateChanged="@((newValue) => OnDateChanged(newValue,context))" Variant="Variant.Filled" DateFormat="yyyy-MM-dd" />
                        </MudTd>
                        <MudTd DataLabel="Description">
                            <MudTextField T="string" Value="@context.Description" ValueChanged="@((newValue) => OnDescriptionChanged(newValue.ToString(),context))" Variant="Variant.Filled" />
                        </MudTd>
                        <MudTd DataLabel="Amount">
                            <MudTextField Value="@context.TransactionAmount" T="decimal" Variant="Variant.Filled" ValueChanged="@((newValue) => OnAmountChanged(newValue, context))"/>
                        </MudTd>
                        <MudTd DataLabel="Category">
                            <MudAutocomplete T="string"
                                Value="@context.Taxonomy.Category"
                                ValueChanged="@((newValue) => OnCategoryChanged(newValue, context))"
                                SearchFunc="@SearchCategories"
                                Dense="true"
                                Placeholder="Select or Add Category" />
                        </MudTd>
                        <MudTd DataLabel="SubCategory">
                            <MudAutocomplete T="string"
                                Value="@context.Taxonomy.SubCategory"
                                ValueChanged="@((newValue) => OnSubCategoryChanged(newValue, context))"
                                SearchFunc="@SearchSubCategories"
                                Dense="true"
                                Placeholder="Select or Add Category" />
                        </MudTd>
                        <MudTd DataLabel="Projected Balance">
                            <MudTextField @bind-Value="@context.ProjectedBalance" T="decimal" Variant="Variant.Filled" />
                        </MudTd>
                    </RowEditingTemplate>
                    <PagerContent>
                        <MudTablePager PageSizeOptions="[25, 50, 200, 250, 1000]" PageSize="25" RowsPerPageString="Transactions per page:" />
                    </PagerContent>
                </MudTable>
            }
            else
            {
                <MudTable Items="@futureTransactions" Striped="true" Hover="true" Bordered="true" Dense="true">
                    <HeaderContent>
                        <MudTh>Date</MudTh>
                        <MudTh>Description</MudTh>
                        <MudTh>Amount</MudTh>
                        <MudTh>Projected Balance</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd DataLabel="Date">@(context.TransactionDate?.ToString("yyyy-MM-dd") ?? "N/A")</MudTd>
                        <MudTd DataLabel="Description">@context.Description</MudTd>
                        <MudTd DataLabel="Amount">@context.TransactionAmount.ToString("C")</MudTd>
                        <MudTd DataLabel="Projected Balance">@context.ProjectedBalance.ToString("C")</MudTd>
                    </RowTemplate>
                </MudTable>
            }
            }
        </ChildContent>
    </MudExpansionPanel>
</MudExpansionPanels>

