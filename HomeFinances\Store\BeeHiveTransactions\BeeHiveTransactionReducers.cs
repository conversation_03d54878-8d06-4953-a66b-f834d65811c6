﻿using Fluxor;
using HomeFinances.Models;
using static HomeFinances.Store.BeeHiveTransactions.BeeHiveTransactionActions;

namespace HomeFinances.Store.BeeHiveTransactions
{
    public class BeeHiveTransactionReducers
    {
        [ReducerMethod]
        public static BeeHiveTransactionState Reduce(BeeHiveTransactionState state, LoadBeehiveTransactionsAction action)
        {
            var newState = state with { LoadState = new LoadingState { Status = LoadingStatusEnum.Loading } };
            return newState;
        }
        [ReducerMethod]
        public static BeeHiveTransactionState Reduce(BeeHiveTransactionState state, LoadBeehiveTransactionsSuccessAction successAction)
        {
            var newState = state with { Transactions = successAction.Transactions, LoadState = new LoadingState { Status = LoadingStatusEnum.Loaded } };
            return newState;
        }

        [ReducerMethod]
        public static BeeHiveTransactionState Reduce(BeeHiveTransactionState state, LoadBeehiveTransactionsFailedAction action)
        {
            var newState = state with { LoadState = new LoadingState { Status = LoadingStatusEnum.Error, ErrorMessage = action.ErrorMessage } };
            return newState;
        }

        [ReducerMethod]
        public static BeeHiveTransactionState Reduce(BeeHiveTransactionState state, UpdateTransactionAction action)
        {
            var updatedTransactions = state.Transactions.Select(t => 
                t.Id == action.Transaction.Id ? action.Transaction : t
            );
            return state with { Transactions = updatedTransactions };
        }
    }
}
