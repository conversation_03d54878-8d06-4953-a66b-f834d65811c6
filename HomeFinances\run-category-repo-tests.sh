        #!/bin/bash
        set -e
        # 1. Create XUnit test project (if not yet created)
        if [[ ! -d "Repositories/CategoryRepositoryTests" ]]; then
          dotnet new xunit -o Repositories/CategoryRepositoryTests
        fi
        rm -f Repositories/CategoryRepositoryTests/UnitTest1.cs
        if [[ -f "Repositories/Tests/AwsDynamoCategoryRepositoryTests.cs" ]]; then
          mv Repositories/Tests/AwsDynamoCategoryRepositoryTests.cs
    Repositories/CategoryRepositoryTests/AwsDynamoCategoryRepositoryTests.cs
        fi
        dotnet add Repositories/CategoryRepositoryTests/CategoryRepositoryTests.csproj reference
    ../../HomeFinances.csproj
        if [[ -f "HomeFinances.sln" ]]; then
          dotnet sln HomeFinances.sln add Repositories/CategoryRepositoryTests/CategoryRepositoryTests.csproj
        fi
        dotnet restore
        dotnet build
        dotnet test Repositories/CategoryRepositoryTests/CategoryRepositoryTests.csproj
