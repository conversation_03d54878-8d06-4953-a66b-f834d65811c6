namespace HomeFinances.Repositories
{
    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using HomeFinances.Models;

    /// <summary>
    /// Repository abstraction for future (scheduled) bank transactions.
    /// </summary>
    public interface IFutureTransactionRepository
    {
        /// <summary>
        /// Gets all future transactions.
        /// </summary>
        Task<IEnumerable<FutureBankTransaction>> GetAllAsync();

        /// <summary>
        /// Gets a future transaction by its identifier.
        /// </summary>
        Task<FutureBankTransaction?> GetByIdAsync(Guid id);

        /// <summary>
        /// Adds a new or updates an existing future transaction.
        /// </summary>
        Task AddOrUpdateAsync(FutureBankTransaction transaction);

        /// <summary>
        /// Deletes a future transaction by its identifier.
        /// </summary>
        Task DeleteAsync(Guid id);
    }
}
