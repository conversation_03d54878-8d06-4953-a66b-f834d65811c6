﻿@inject IDialogService DialogService

<MudDialog>
    <DialogContent>
        <MudForm Model="Transaction">
            <MudTextField T="string" Label="Description" @bind-Value="Transaction.Description" />
            <MudSelect T="int" Label="Day of Month" MultiSelection="false" @bind-Value="Transaction.DayOfMonth">
                @for (int day = 1; day <= 31; day++)
                {
                    <MudSelectItem Value="@day" />
                }
            </MudSelect>
            
            <MudTextField Label="Amount" @bind-Value="Transaction.Amount"  FullWidth="true"/>
            <MudDatePicker Label="End Date" @bind-Date="@Transaction.EndDateTime" DateFormat="yyyy-MM-dd" />
            <MudSelect Label="Outgo" @bind-Value="Transaction.IsOutgo">
                <MudSelectItem Value="@(true)" />
                <MudSelectItem Value="@(false)" />
            </MudSelect>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton Color="Color.Primary" OnClick="@(() => Submit())">Submit</MudButton>
        <MudButton OnClick="@Cancel">Cancel</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; }
    [Parameter] public RecurringTransaction Transaction { get; set; } = new RecurringTransaction();


    private void Submit()
    {
        MudDialog.Close(DialogResult.Ok(Transaction));
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }
}