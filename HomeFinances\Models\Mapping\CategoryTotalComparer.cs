﻿namespace HomeFinances.Models.Mapping
{
    public class CategoryTotalComparer : IEqualityComparer<(string Category, decimal Total)>
    {
        public bool Equals((string Category, decimal Total) x, (string Category, decimal Total) y)
        {
            return x.Category == y.Category;
        }

        public int GetHashCode((string Category, decimal Total) obj)
        {
            return obj.Category.GetHashCode();
        }
    }

}
