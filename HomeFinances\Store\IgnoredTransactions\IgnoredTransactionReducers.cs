﻿using Fluxor;
using HomeFinances.Models;
using static HomeFinances.Store.IgnoredTransactions.IgnoredTransactionActions;

namespace HomeFinances.Store.IgnoredTransactions
{
    public class IgnoredTransactionReducers
    {
        [ReducerMethod]
        public static IgnoredTransactionsState ReduceLoadIgnoredTransactionsAction(IgnoredTransactionsState state, LoadIgnoredTransactionsAction action)
            => state with { Loading = new LoadingState { Status = LoadingStatusEnum.Loading } };

        [ReducerMethod]
        public static IgnoredTransactionsState ReduceLoadIgnoredTransactionsSuccessAction(IgnoredTransactionsState state, LoadIgnoredTransactionsSuccessAction action)
            => state with { IgnoredTransactions = action.Transactions, Loading = new LoadingState { Status = LoadingStatusEnum.Loaded } };

        [ReducerMethod]
        public static IgnoredTransactionsState ReduceIgnoreTransactionAction(IgnoredTransactionsState state, IgnoreTransactionAction action)
            => state with { Loading = new LoadingState { Status = LoadingStatusEnum.Loading } };

        [ReducerMethod]
        public static IgnoredTransactionsState ReduceIgnoreTransactionSuccessAction(IgnoredTransactionsState state, IgnoreTransactionSuccessAction action)
        {
            var updatedTransactions = state.IgnoredTransactions.Append(action.Transaction).ToList();
            return state with { IgnoredTransactions = updatedTransactions, Loading = new LoadingState { Status = LoadingStatusEnum.Loaded } };
        }

        [ReducerMethod]
        public static IgnoredTransactionsState ReduceIgnoreTransactionFailedAction(IgnoredTransactionsState state, IgnoreTransactionFailedAction action)
            => state with { Loading = new LoadingState { Status = LoadingStatusEnum.Error, ErrorMessage = action.ErrorMessage } };
    }
}