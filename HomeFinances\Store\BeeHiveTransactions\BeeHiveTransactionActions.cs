﻿using HomeFinances.Models;

namespace HomeFinances.Store.BeeHiveTransactions
{
    public record BeeHiveTransactionActions
    {
        public record LoadBeehiveTransactionsAction();
        public record LoadBeehiveTransactionsSuccessAction(IEnumerable<BeeHiveTransaction> Transactions);
        public record LoadBeehiveTransactionsFailedAction(string ErrorMessage);
        public record UpdateTransactionAction(BeeHiveTransaction Transaction);
    }
}
