using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Amazon;
using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.Model;
using Xunit;
using HomeFinances.Repositories;
using CSharpFunctionalExtensions;

namespace CategoryRepositoryTests
{
    /// <summary>
    /// Integration tests for AwsDynamoCategoryRepository against DynamoDB Local.
    /// Requires a local DynamoDB endpoint at http://localhost:8000.
    /// </summary>
    public class AwsDynamoCategoryRepositoryIntegrationTests : IDisposable
    {
        private const string TableName = "TestCategoriesIntegration";
        private readonly IAmazonDynamoDB _client;
        private readonly AwsDynamoCategoryRepository _repository;

        public AwsDynamoCategoryRepositoryIntegrationTests()
        {
            // Configure DynamoDB Local client
            var config = new AmazonDynamoDBConfig { ServiceURL = "http://localhost:8000", UseHttp = true };
            _client = new AmazonDynamoDBClient(config);

            // Create table for integration tests
            var createRequest = new CreateTableRequest
            {
                TableName = TableName,
                KeySchema = new List<KeySchemaElement>
                {
                    new KeySchemaElement("Category", KeyType.HASH)
                },
                AttributeDefinitions = new List<AttributeDefinition>
                {
                    new AttributeDefinition("Category", ScalarAttributeType.S)
                },
                ProvisionedThroughput = new ProvisionedThroughput(1, 1)
            };
            _client.CreateTableAsync(createRequest).GetAwaiter().GetResult();
            // Wait for table to become ACTIVE
            WaitForTableToBecomeActive(TableName);

            _repository = new AwsDynamoCategoryRepository(_client);
        }

        private void WaitForTableToBecomeActive(string tableName)
        {
            string status = null;
            do
            {
                Thread.Sleep(500);
                var resp = _client.DescribeTableAsync(new DescribeTableRequest { TableName = tableName }).GetAwaiter().GetResult();
                status = resp.Table.TableStatus;
            } while (status != TableStatus.ACTIVE.Value);
        }

        [Fact]
        public async Task<Result<IList<string>>> GetAllCategoriesAsync_ReturnsAll()
        {
            const string cat1 = "IntCat1";
            const string cat2 = "IntCat2";
            await _repository.AddSubCategoryAsync(cat1, "Sub1");
            await _repository.AddSubCategoryAsync(cat2, "Sub2");
            var all = await _repository.GetAllCategoriesAsync();
            return Result.Success<IList<string>>(all);
        }

        [Fact]
        public async Task<Result> AddAndGetSubCategoryAsync_Works()
        {
            const string category = "IntegrationFood";
            const string subCategory = "Groceries";
            await _repository.AddSubCategoryAsync(category, subCategory);
            var subsResult = await _repository.GetSubCategoriesAsync(category);
            if (subsResult.IsFailure)
            {
                return Result.Failure(subsResult.Error);
            }
            var subs = subsResult.Value;
            return subs.Count == 1 && subs.Contains(subCategory) ? Result.Success() : Result.Failure("Subcategory not found");
        }

        [Fact]
        public async Task<Result> RemoveSubCategoryAsync_Works()
        {
            const string category = "IntegrationBills";
            const string sub1 = "Electricity";
            const string sub2 = "Water";
            await _repository.AddSubCategoryAsync(category, sub1);
            await _repository.AddSubCategoryAsync(category, sub2);
            // Remove one
            var removedResult = await _repository.RemoveSubCategoryAsync(category, sub1);
            if (removedResult.IsFailure)
            {
                return Result.Failure(removedResult.Error);
            }
            var subsResult = await _repository.GetSubCategoriesAsync(category);
            if (subsResult.IsFailure)
            {
                return Result.Failure(subsResult.Error);
            }
            var subs = subsResult.Value;
            return subs.Count == 1 && subs.Contains(sub2) ? Result.Success() : Result.Failure("Subcategory not found");
        }

        public void Dispose()
        {
            try
            {
                _client.DeleteTableAsync(new DeleteTableRequest { TableName = TableName }).GetAwaiter().GetResult();
            }
            catch { /* ignore */ }
            _client.Dispose();
        }
    }
}