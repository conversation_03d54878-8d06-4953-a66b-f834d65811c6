﻿using System.Text.Json.Serialization;

namespace HomeFinances.Models.ChatGPT
{
    public class TextMessage : ComplexMessage
    {
        [JsonPropertyName("role")]
        public new string Role { get; set; } = string.Empty;

        [JsonPropertyName("content")]
        public new string Content { get; set; } = string.Empty;

        public TextMessage(string prompt)
        {
            Content = new TextContent(prompt).Text;
        }
    }
}