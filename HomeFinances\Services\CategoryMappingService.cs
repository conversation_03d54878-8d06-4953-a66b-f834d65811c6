using CSharpFunctionalExtensions;
using HomeFinances.Models;
using HomeFinances.Repositories;

namespace HomeFinances.Services;

/// <summary>
///     Service for managing category mappings, delegating storage to ICategoryMappingRepository.
/// </summary>
public class CategoryMappingService
{
    private readonly ChatGptApiService _chatGptApiService;
    private readonly ICategoryMappingRepository _mappingRepository;

    public CategoryMappingService(ChatGptApiService chatGptApiService
        , ICategoryMappingRepository mappingRepository)
    {
        _chatGptApiService = chatGptApiService;
        _mappingRepository = mappingRepository;
    }

    public async Task<Taxonomy> GetCategoryAndSubCategory(string description)
    {
        var mappingResult = await _mappingRepository.GetMappingByDescriptionAsync(description: description);
        if (mappingResult.IsSuccess && mappingResult.Value != null)
        {
            var mapping = mappingResult.Value;
            return new Taxonomy { Category = mapping.Category, SubCategory = mapping.SubCategory };
        }

        return new Taxonomy(); // Return empty Taxonomy if not found or error
    }

    public Task<Result> AddMapping(string description
        , string category
        , string subCategory)
    {
        var mapping = new CategoryMapping { Description = description, Category = category, SubCategory = subCategory };
        return _mappingRepository.AddOrUpdateMappingAsync(mapping: mapping);
    }

    public async Task<bool> RemoveMappingAsync(string description)
    {
        var result = await _mappingRepository.RemoveMappingAsync(description: description);
        return result.IsSuccess && result.Value;
    }

    /// <summary>
    ///     Upserts the mapping for the specified description.
    /// </summary>
    public Task<Result> UpdateOrAddMapping(string description
        , string category
        , string subCategory)
    {
        var mapping = new CategoryMapping { Description = description, Category = category, SubCategory = subCategory };
        return _mappingRepository.AddOrUpdateMappingAsync(mapping: mapping);
    }
}