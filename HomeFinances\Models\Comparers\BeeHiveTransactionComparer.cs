﻿namespace HomeFinances.Models.Comparers
{
    public class BeeHiveTransactionComparer : IEqualityComparer<BeeHiveTransaction>
    {
        public bool Equals(BeeHiveTransaction? x, BeeHiveTransaction? y)
        {
            if (ReferenceEquals(x, y)) return true;
            if (ReferenceEquals(x, null)) return false;
            if (ReferenceEquals(y, null)) return false;
            if (x.GetType() != y.GetType()) return false;
            return x.Suffix == y.Suffix && x.SequenceNumber == y.SequenceNumber;
        }

        public int GetHashCode(BeeHiveTransaction obj)
        {
            return HashCode.Combine(obj.Suffix, obj.SequenceNumber);
        }
    }
}
