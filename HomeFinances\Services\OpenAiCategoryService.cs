using System.Text.Json;
using HomeFinances.Models;

namespace HomeFinances.Services;

public class OpenAiCategoryService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<OpenAiCategoryService> _logger;

    public OpenAiCategoryService(HttpClient httpClient, ILogger<OpenAiCategoryService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        
        var apiKey = Environment.GetEnvironmentVariable("OPENAI_API_KEY") ?? 
            throw new InvalidOperationException("OPENAI_API_KEY environment variable not set");
            
        _httpClient.BaseAddress = new Uri("https://api.openai.com/v1/");
        _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {apiKey}");
    }

    public async Task<(string Category, string SubCategory)> GetCategorySuggestion(string transactionDescription, decimal amount)
    {
        try
        {
            var messages = new[]
            {
                new { role = "system", content = "You are a financial categorization assistant. Given a transaction description and amount, suggest a category and subcategory. Respond in JSON format with exactly two fields: 'category' and 'subCategory'. Keep categories general like 'Food', 'Healthcare', 'Entertainment', etc. and subcategories more specific." },
                new { role = "user", content = $"Transaction: '{transactionDescription}' Amount: {amount:C}" }
            };

            var requestBody = new { model = "gpt-3.5-turbo", messages = messages, temperature = 0.7, max_tokens = 150 };
            var response = await _httpClient.PostAsJsonAsync("chat/completions", requestBody);
            
            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("OpenAI API error: {StatusCode} {Content}", response.StatusCode, errorContent);
                throw new HttpRequestException($"OpenAI API error: {response.StatusCode}");
            }

            var responseBody = await response.Content.ReadAsStringAsync();
            using var jsonDoc = JsonDocument.Parse(responseBody);
            var content = jsonDoc.RootElement
                .GetProperty("choices")[0]
                .GetProperty("message")
                .GetProperty("content")
                .GetString() ?? "{}";

            using var contentDoc = JsonDocument.Parse(content);
            var root = contentDoc.RootElement;
            return (
                root.TryGetProperty("category", out var cat) ? cat.GetString() ?? "Unknown" : "Unknown",
                root.TryGetProperty("subCategory", out var subCat) ? subCat.GetString() ?? "Unknown" : "Unknown"
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting category suggestion for transaction {Description}", transactionDescription);
            throw; // Let the component handle the error
        }
    }
} 