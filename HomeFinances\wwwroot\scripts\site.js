function registerResizeListener(dotNetReference) {
    if (!dotNetReference) {
        console.error("Invalid .NET reference passed to registerResizeListener");
        return;
    }

    window.addEventListener("resize", () => {
        try {
            if (dotNetReference) {
                dotNetReference.invokeMethodAsync("OnBrowserResize", window.innerWidth);
            }
        } catch (error) {
            console.error("Error calling .NET method from resize listener:", error);
        }
    });
}

window.blazorResize = {
    getInnerWidth: function () {
        return window.innerWidth;
    }
};

window.registerResizeListener = registerResizeListener;

// Initialize download helper
(function() {
    window.downloadHelper = {
        downloadFileFromStream: function(fileName, base64String) {
            try {
                console.log("Downloading file:", fileName);
                const link = document.createElement('a');
                link.download = fileName;
                link.href = "data:text/csv;charset=utf-8;base64," + base64String;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } catch (error) {
                console.error("Error in downloadFileFromStream:", error);
            }
        }
    };
    console.log("Download helper initialized");
})();
