using System.Collections.Generic;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using HomeFinances.Models;

namespace HomeFinances.Repositories
{
    /// <summary>
    /// Repository abstraction for category mapping (description to category and subcategory).
    /// </summary>
    public interface ICategoryMappingRepository
    {
        /// <summary>
        /// Gets all category mappings.
        /// </summary>
        Task<Result<List<CategoryMapping>>> GetAllMappingsAsync();

        /// <summary>
        /// Gets the mapping for the specified description.
        /// </summary>
        Task<Result<CategoryMapping>> GetMappingByDescriptionAsync(string description);

        /// <summary>
        /// Adds or updates the specified mapping.
        /// </summary>
        Task<Result> AddOrUpdateMappingAsync(CategoryMapping mapping);

        /// <summary>
        /// Removes the mapping for the specified description.
        /// Returns true if removed; false otherwise.
        /// </summary>
        Task<Result<bool>> RemoveMappingAsync(string description);
    }
}