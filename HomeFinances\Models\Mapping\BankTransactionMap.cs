﻿using CsvHelper.Configuration;

namespace HomeFinances.Models.Mapping
{
    public sealed class BankTransactionMap : ClassMap<BankTransaction>
    {
        public BankTransactionMap()
        {
            Map(m => m.Account).Name("Account");
            Map(m => m.TransactionDate).Name("Transaction Date");
            Map(m => m.TransactionAmount).Name("Transaction Amount").TypeConverter<CurrencyConverter>();
            Map(m => m.Description).Name("Description");
            Map(m => m.Balance).Name("Balance").TypeConverter<CurrencyConverter>();
            Map(m => m.BankCode).Name("BankCode");
        }
    }
}