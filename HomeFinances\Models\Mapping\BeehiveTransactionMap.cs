﻿using CsvHelper.Configuration;

namespace HomeFinances.Models.Mapping
{
    namespace HomeFinances.Models.Mapping
    {
        public sealed class BeeHiveTransactionMap : ClassMap<BeeHiveTransaction>
        {
            public BeeHiveTransactionMap()
            {
                Map(m => m.Account).Name("Account");
                Map(m => m.Suffix).Name("Suffix");
                Map(m => m.SequenceNumber).Name("Sequence Number");
                Map(m => m.TransactionDate).Name("Transaction Date");
                Map(m => m.TransactionAmount).Name("Transaction Amount").TypeConverter<CurrencyConverter>();
                Map(m => m.Description).Name("Description");
                Map(m => m.ExtendedDescription).Name("Extended Description");
                Map(m => m.ElectronicTransactionDate).Name("Electronic Transaction Date");
                Map(m => m.ElectronicTransactionTime).Name("Electronic Transaction Time");
                Map(m => m.Balance).Name("Balance").TypeConverter<CurrencyConverter>();
            }
        }
    }
}
