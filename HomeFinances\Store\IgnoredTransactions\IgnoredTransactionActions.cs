﻿using HomeFinances.Models;

namespace HomeFinances.Store.IgnoredTransactions
{
    public class IgnoredTransactionActions
    {
        public record LoadIgnoredTransactionsAction();
        public record LoadIgnoredTransactionsSuccessAction(List<PossiblyMissingTransactionViewModel> Transactions);
        public record IgnoreTransactionAction(PossiblyMissingTransactionViewModel Transaction);
        public record IgnoreTransactionSuccessAction(PossiblyMissingTransactionViewModel Transaction);
        public record IgnoreTransactionFailedAction(string ErrorMessage);
    }
}
