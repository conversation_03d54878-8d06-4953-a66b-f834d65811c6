﻿using Fluxor;
using HomeFinances.Models;

namespace HomeFinances.Store.BankTransactions
{
    [FeatureState]
    public record BankTransactionState
    {
        public IEnumerable<BankTransaction> Transactions { get; set; }
        public LoadingState Loading { get; set; } = new LoadingState();

        public BankTransactionState()
        {
            Transactions = new List<BankTransaction>();
        }
    }
}