﻿using CsvHelper.Configuration;

namespace HomeFinances.Models.Mapping;

public class FutureTransactionMap : ClassMap<FutureBankTransaction>
{
    public FutureTransactionMap()
    {
        Map(expression: m => m.Id).Name("Id");
        Map(expression: m => m.TransactionAmount).Name("TransactionAmount");
        Map(expression: m => m.Description).Name("Description");
        Map(expression: m => m.Taxonomy.Category).Name("Category");
        Map(expression: m => m.Taxonomy.SubCategory).Name("SubCategory");
        Map(expression: m => m.Taxonomy.Confidence).Name("Confidence");
        Map(expression: m => m.TransactionDate).Name("TransactionDate")
           .TypeConverter(typeConverter: new FlexibleDateTimeConverter());
        Map(expression: m => m.Enabled).Name("Enabled");
    }
}