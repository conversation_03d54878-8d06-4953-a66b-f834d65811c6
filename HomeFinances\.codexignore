# Build output
bin/
obj/
publish/

# User settings
.vscode/
.idea/

# Git
.git/
.gitignore

# System files
*.DS_Store
Thumbs.db

# Dotnet-specific junk
*.dll
*.pdb
*.exe
*.cache
*.log
*.user
*.suo

# Test output or coverage
TestResults/
coverage/

# Misc package systems
node_modules/

# Dotnet project metadata
*.csproj
*.sln
*.props
*.targets

# JSON files (often output/config/metadata)
*.json

# User data, receipts, test files, exports, etc.
Data/

# Ignore Azurite emulator artifacts if used
__azurite_db_*/
__queuestorage__/
__blobstorage__/

# Other large static assets
wwwroot/
