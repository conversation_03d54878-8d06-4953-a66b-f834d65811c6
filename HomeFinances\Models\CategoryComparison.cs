﻿namespace HomeFinances.Models
{
    public class CategoryComparison
    {
        public string Category { get; set; }
        public decimal BankTotal { get; set; }
        public decimal BeeHiveTotal { get; set; }
        public decimal FutureTotal { get; set; }
        public decimal Difference => FutureTotal - BankTotal;

        public CategoryComparison()
        {
            Category = string.Empty;
        }
    }
}
