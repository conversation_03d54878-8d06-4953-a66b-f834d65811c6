﻿using Fluxor;
using HomeFinances.Components.Pages;
using HomeFinances.Models;
using HomeFinances.Store.BankTransactions;
using static HomeFinances.Store.FutureTransactions.FutureTransactionActions;

namespace HomeFinances.Store.FutureTransactions
{
    public class FutureTransactionReducers
    {
        [ReducerMethod]
        public static FutureTransactionState Reduce(FutureTransactionState state, LoadFutureTransactionsSuccessAction successAction)
        {
            var newState = state with { FutureTransactions = successAction.Transactions, Loading = new LoadingState { Status = LoadingStatusEnum.Loaded } };
            return newState;
        }

        [ReducerMethod]
        public static FutureTransactionState Reduce(FutureTransactionState state, LoadFutureTransactionsFailedAction failedAction)
        {
            var newState = state with { Loading = new LoadingState { Status = LoadingStatusEnum.Error, ErrorMessage = failedAction.ErrorMessage } };
            return newState;
        }
        //[ReducerMethod]
        //public static FutureTransactionState Reduce(FutureTransactionState state, UpdateFutureTransactionsAction action)
        //{
        //    // Optionally set a loading state here
        //    return state with { Loading = new LoadingState { Status = LoadingStatusEnum.Loading } };
        //}

        //[ReducerMethod]
        //public static FutureTransactionState Reduce(FutureTransactionState state, UpdateFutureTransactionsSuccessAction successAction)
        //{
        //    return state with { FutureTransactions = successAction.Transactions, Loading = new LoadingState { Status = LoadingStatusEnum.Loaded } };
        //}

        //[ReducerMethod]
        //public static FutureTransactionState Reduce(FutureTransactionState state, UpdateFutureTransactionsFailedAction failedAction)
        //{
        //    return state with { Loading = new LoadingState { Status = LoadingStatusEnum.Error, ErrorMessage = failedAction.ErrorMessage } };
        //}

        // Handling transaction deletions
        [ReducerMethod]
        public static FutureTransactionState Reduce(FutureTransactionState state, DeleteFutureTransactionSucceededAction action)
        {
            return state with { FutureTransactions = action.transactions };
        }

        [ReducerMethod]
        public static FutureTransactionState Reduce(FutureTransactionState state, ClearAllFutureTransactionsAction action)
        {
            return state with { Loading = new LoadingState { Status = LoadingStatusEnum.Loading } };
        }

        [ReducerMethod]
        public static FutureTransactionState Reduce(FutureTransactionState state, ClearAllFutureTransactionsSucceededAction action)
        {
            return state with { FutureTransactions = new List<FutureBankTransaction>(), Loading = new LoadingState { Status = LoadingStatusEnum.Loaded } };
        }

        [ReducerMethod]
        public static FutureTransactionState Reduce(FutureTransactionState state, ClearAllFutureTransactionsFailedAction action)
        {
            return state with { Loading = new LoadingState { Status = LoadingStatusEnum.Error, ErrorMessage = action.ErrorMessage } };
        }

        // Handling disabling transactions
        [ReducerMethod]
        public static FutureTransactionState Reduce(FutureTransactionState state, DisableFutureTransactionAction action)
        {
            var updatedTransactions = state.FutureTransactions.Select(t =>
                t.Id == action.transaction.Id ? t with { Enabled = false } : t);

            return state with { FutureTransactions = updatedTransactions };
        }

        // Handling date updates on transactions
        [ReducerMethod]
        public static FutureTransactionState Reduce(FutureTransactionState state, UpdateFutureTransactionDateAction action)
        {
            var updatedTransactions = state.FutureTransactions.Select(t =>
                t.Id == action.transaction.Id ? t with { TransactionDate = action.newDate } : t);

            return state with { FutureTransactions = updatedTransactions };
        }

        [ReducerMethod]
        public static FutureTransactionState Reduce(FutureTransactionState state, UpdateFutureTransactionSucceededAction successAction)
        {
            return state with { FutureTransactions = successAction.transactions, Loading = new LoadingState { Status = LoadingStatusEnum.Loaded } };
        }

        [ReducerMethod]
        public static FutureTransactionState Reduce(FutureTransactionState state, UpdateFutureTransactionFailedAction failedAction)
        {
            return state with { Loading = new LoadingState { Status = LoadingStatusEnum.Error, ErrorMessage = failedAction.ErrorMessage } };
        }

        
    }
}
