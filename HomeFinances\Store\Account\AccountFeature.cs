using Fluxor;
using System;
using System.Collections.Generic;

namespace HomeFinances.Store.Account
{
    public class AccountFeature : Feature<AccountState>
    {
        public override string GetName() => "Account";

        protected override AccountState GetInitialState() =>
            new AccountState(
                currentAccountId: Guid.Parse("********-0000-0000-0000-********0001"), // Default account
                availableAccounts: new List<AccountInfo>
                {
                    new AccountInfo(Guid.Parse("********-0000-0000-0000-********0001"), "BeeHive"),
                    new AccountInfo(Guid.Parse("********-0000-0000-0000-********0002"), "CitiBank"),
                    new AccountInfo(Guid.Parse("********-0000-0000-0000-************"), "MountainAmerica")
                }
            );
    }
}
