﻿using System.Text.Json.Serialization;

namespace HomeFinances.Models.ChatGPT
{
    public class Choice
    {
        [JsonPropertyName("index")]
        public int Index { get; set; }

        [JsonPropertyName("message")]
        public Message Message { get; set; }

        [JsonPropertyName("logprobs")]
        public object Logprobs { get; set; } // Keep as object if you're not using it, or define a structure if you are

        [JsonPropertyName("finish_reason")]
        public string FinishReason { get; set; }

        public Choice()
        {
            Message = new Message();
            Logprobs = new object();
            FinishReason = string.Empty;
        }
    }
}