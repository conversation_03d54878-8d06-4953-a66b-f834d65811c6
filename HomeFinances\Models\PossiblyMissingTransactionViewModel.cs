﻿namespace HomeFinances.Models;

public class PossiblyMissingTransactionViewModel : RecurringTransaction
{
    public PossiblyMissingTransactionViewModel()
    {
    }

    public PossiblyMissingTransactionViewModel(BankTransaction transaction
        , int displayIndex = 0)
    {
        if (transaction == null) throw new ArgumentNullException(paramName: nameof(transaction));

        Id = transaction.Id;
        Description = transaction.Description;
        TransactionDate = transaction.TransactionDate;
        Amount = transaction.TransactionAmount;
        DayOfMonth = transaction.TransactionDate.Value.Day;
        IsChecked = false;
        IgnoreUntil = null;
        DisplayIndex = displayIndex;
    }

    public bool IsChecked { get; set; }
    public DateTime? TransactionDate { get; set; }
    public int DisplayIndex { get; set; }

    private DateTime CalculateNextTransactionDate(int dayOfMonth)
    {
        if (dayOfMonth == 0) return DateTime.Now.AddDays(value: -20);
        var nextDate = new DateTime(year: DateTime.Today.Year
                                    , month: DateTime.Today.Month
                                    , day: dayOfMonth);
        return nextDate < DateTime.Today
            ? nextDate.AddMonths(months: 1)
            : nextDate;
    }
}