﻿using Fluxor;
using HomeFinances.Repositories;
using HomeFinances.Services;

namespace HomeFinances.Store.GeneratedImages;

public class GeneratedImagesEffects
{
    private readonly ChatGptApiService _chatGptApiService;
    private readonly ILogger<GeneratedImagesEffects> _logger;
    private readonly string _partitionKey = "feathers";
    private readonly ImageGenerationRepository _repository;

    public GeneratedImagesEffects(ImageGenerationRepository repository
        , ChatGptApiService chatGptApiService
        , ILogger<GeneratedImagesEffects> logger)
    {
        _repository = repository;
        _chatGptApiService = chatGptApiService;
        _logger = logger;
    }

    [EffectMethod]
    public async Task HandleLoadGeneratedImagesAction(GeneratedImagesActions.LoadGeneratedImagesAction action
        , IDispatcher dispatcher)
    {
        try
        {
            var images = await _repository.GetAllImageGenerationsAsync();
            if (images.IsFailure)
            {
                _logger.LogError(message: "Failed to load generated images: {Error}"
                                 , images.Error);
                dispatcher.Dispatch(
                    action: new GeneratedImagesActions.LoadGeneratedImagesFailureAction(ErrorMessage: images.Error));
                return;
            }

            var generatedImages = images.Value.Select(selector: x => new GeneratedImage(Prompt: x.RowKey
                                                          , ImageUrl: x.ImageUrl
                                                          , Cost: x.CostDecimal)).ToList();
            dispatcher.Dispatch(action: new GeneratedImagesActions.LoadGeneratedImagesSuccessAction(Images: generatedImages));
        }
        catch (Exception ex)
        {
            _logger.LogError(exception: ex
                             , message: "Error loading generated images");
            dispatcher.Dispatch(action: new GeneratedImagesActions.LoadGeneratedImagesFailureAction(ErrorMessage: ex.Message));
        }
    }

    [EffectMethod]
    public async Task HandleAddGeneratedImageAction(GeneratedImagesActions.AddGeneratedImageAction action
        , IDispatcher dispatcher)
    {
        try
        {
            var imageUrl = await _chatGptApiService.GenerateImageFromDescription(description: action.Prompt);
            if (!string.IsNullOrEmpty(value: imageUrl))
            {
                var image = new GeneratedImage(Prompt: action.Prompt
                                               , ImageUrl: imageUrl
                                               , Cost: action.Cost);
                var addResult = await _repository.AddOrUpdateImageGenerationAsync(partitionKey: _partitionKey
                                                                                  , rowKey: image.Prompt
                                                                                  , cost: action.Cost
                                                                                  , imageUrl: imageUrl);
                if (addResult.IsFailure) return; //TODO This should return a result.

                dispatcher.Dispatch(action: new GeneratedImagesActions.AddGeneratedImageSuccessAction(Image: image));
            }
            else
            {
                dispatcher.Dispatch(
                    action: new GeneratedImagesActions.AddGeneratedImageFailureAction(ErrorMessage: "Failed to generate image."));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(exception: ex
                             , message: "Error adding generated image");
            dispatcher.Dispatch(action: new GeneratedImagesActions.AddGeneratedImageFailureAction(ErrorMessage: ex.Message));
        }
    }

    [EffectMethod]
    public async Task HandleDeleteGeneratedImageAction(GeneratedImagesActions.DeleteGeneratedImageAction action
        , IDispatcher dispatcher)
    {
        try
        {
            await _repository.DeleteImageGenerationAsync(partitionKey: action.PartitionKey
                                                         , rowKey: action.RowKey);
            dispatcher.Dispatch(
                action: new GeneratedImagesActions.DeleteGeneratedImageSuccessAction(PartitionKey: action.PartitionKey
                                                                                     , RowKey: action.RowKey));
        }
        catch (Exception ex)
        {
            _logger.LogError(exception: ex
                             , message: "Error deleting generated image");
            dispatcher.Dispatch(action: new GeneratedImagesActions.DeleteGeneratedImageFailureAction(ErrorMessage: ex.Message));
        }
    }

    [EffectMethod]
    public async Task HandleUpdateGeneratedImageAction(GeneratedImagesActions.UpdateGeneratedImageAction action
        , IDispatcher dispatcher)
    {
        try
        {
            var imageUrl = await _chatGptApiService.GenerateImageFromDescription(description: action.Prompt);
            if (!string.IsNullOrEmpty(value: imageUrl))
            {
                var image = new GeneratedImage(Prompt: action.Prompt
                                               , ImageUrl: imageUrl
                                               , Cost: action.Cost);
                await _repository.AddOrUpdateImageGenerationAsync(partitionKey: _partitionKey
                                                                  , rowKey: image.Prompt
                                                                  , cost: action.Cost
                                                                  , imageUrl: imageUrl);
                dispatcher.Dispatch(action: new GeneratedImagesActions.UpdateGeneratedImageSuccessAction(Image: image));
            }
            else
            {
                dispatcher.Dispatch(
                    action: new GeneratedImagesActions.UpdateGeneratedImageFailureAction(
                        ErrorMessage: "Failed to regenerate image."));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(exception: ex
                             , message: "Error updating generated image");
            dispatcher.Dispatch(action: new GeneratedImagesActions.UpdateGeneratedImageFailureAction(ErrorMessage: ex.Message));
        }
        finally
        {
            dispatcher.Dispatch(action: new GeneratedImagesActions.SetRegeneratingImageAction(Prompt: action.Prompt
                                    , IsRegenerating: false));
        }
    }
}