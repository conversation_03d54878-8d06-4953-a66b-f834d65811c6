using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using HomeFinances.Repositories;
using System.Collections.Generic;
using CSharpFunctionalExtensions;

namespace CategoryRepositoryTests
{
    public class FileCategoryRepositoryTests : IDisposable
    {
        private readonly string _tempFilePath;
        private readonly FileCategoryRepository _repository;

        public FileCategoryRepositoryTests()
        {
            _tempFilePath = Path.Combine(Path.GetTempPath(), Guid.NewGuid() + ".csv");
            _repository = new FileCategoryRepository(_tempFilePath);
        }

        public void Dispose()
        {
            if (File.Exists(_tempFilePath))
                File.Delete(_tempFilePath);
        }

        [Fact]
        public async Task<Result> AddSubCategoryAsync_NewCategory_CreatesCategory()
        {
            var result = await _repository.AddSubCategoryAsync("Food", "Groceries");
            Assert.True(result.IsSuccess);
            var all = await _repository.GetAllCategoriesAsync();
            Assert.Single(all.Value);
            Assert.Contains("Food", all.Value.Keys);
            Assert.Equal(new List<string> { "Groceries" }, all.Value["Food"]);
            return result;
        }

        [Fact]
        public async Task<Result> AddSubCategoryAsync_ExistingCategory_AddsSubCategory()
        {
            await _repository.AddSubCategoryAsync("Food", "Groceries");
            var result = await _repository.AddSubCategoryAsync("Food", "Restaurants");
            Assert.True(result.IsSuccess);
            var subs = (await _repository.GetSubCategoriesAsync("Food")).Value.ToList();
            Assert.Equal(2, subs.Count);
            Assert.Contains("Groceries", subs);
            Assert.Contains("Restaurants", subs);
            return result;
        }

        [Fact]
        public async Task<Result> RemoveSubCategoryAsync_RemovesExistingSubCategory()
        {
            await _repository.AddSubCategoryAsync("Food", "Groceries");
            var removedResult = await _repository.RemoveSubCategoryAsync("Food", "Groceries");
            var subs = (await _repository.GetSubCategoriesAsync("Food")).Value.ToList();
            Assert.True(removedResult.IsSuccess);
            Assert.Empty(subs);
            return removedResult;
        }

        [Fact]
        public async Task<Result> RemoveSubCategoryAsync_NonExisting_ReturnsFalse()
        {
            var removedResult = await _repository.RemoveSubCategoryAsync("Food", "Nonexistent");
            Assert.False(removedResult.IsSuccess);
            return removedResult;
        }

        [Fact]
        public async Task<Result> GetSubCategoriesAsync_NonExistingCategory_ReturnsEmpty()
        {
            var subsResult = await _repository.GetSubCategoriesAsync("NonExist");
            Assert.True(subsResult.IsSuccess);
            Assert.Empty(subsResult.Value);
            return subsResult;
        }
    }
}