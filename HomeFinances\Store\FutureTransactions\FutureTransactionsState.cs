﻿using Fluxor;
using HomeFinances.Models;

namespace HomeFinances.Store.FutureTransactions
{
    [FeatureState]
    public record FutureTransactionState
    {
        public IEnumerable<FutureBankTransaction> FutureTransactions { get; set; }
        public LoadingState Loading { get; set; } = new LoadingState();

        public FutureTransactionState()
        {
            FutureTransactions = new List<FutureBankTransaction>();
        }
    }
}
