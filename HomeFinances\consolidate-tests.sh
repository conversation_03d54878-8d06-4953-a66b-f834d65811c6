#!/bin/bash
        set -e
        # Safely consolidate all test projects/files into HomeFinances.Tests (update as needed for your repo).
        git add .
        git commit -m "WIP: Pre-test-project-consolidation commit."
        git checkout -b consolidate-tests
        rm -f ../HomeFinances.Tests/UnitTest1.cs
        if [ -f ../Repositories/CategoryRepositoryTests/AwsDynamoCategoryRepositoryTests.cs ]; then
          mv ../Repositories/CategoryRepositoryTests/AwsDynamoCategoryRepositoryTests.cs
    ../HomeFinances.Tests/AwsDynamoCategoryRepositoryTests.cs
        fi
        if [ -d ../HomeFinances.Text ]; then
          mv ../HomeFinances.Text/*.cs ../HomeFinances.Tests/ || true
        fi
        dotnet sln ../HomeFinances.sln remove ../HomeFinances.Text/HomeFinances.Text.csproj || true
        dotnet sln ../HomeFinances.sln remove ../Repositories/CategoryRepositoryTests/CategoryRepositoryTests.csproj ||
    true
        dotnet sln ../HomeFinances.sln add ../HomeFinances.Tests/HomeFinances.Tests.csproj
        rm -rf ../HomeFinances.Text ../Repositories/CategoryRepositoryTests
        git add .
        git commit -m "Consolidated all tests into HomeFinances.Tests, removed extra test projects."
        echo "Test project consolidation complete!"
