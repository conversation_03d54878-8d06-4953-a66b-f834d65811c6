#!/usr/bin/env bash
# verify-secrets.sh
# Fails the workflow if any deployment variable is unset or empty.
# Secrets are automatically masked by GitHub Actions, so log output is safe.

set -euo pipefail

# List every variable the workflow needs
REQUIRED_VARS=(
  AWS_REGION
  AWS_ROLE_ARN
  APPLICATION_NAME
  ENVIRONMENT_NAME
  S3_BUCKET
  VERSION_LABEL
)

missing=false

for var in "${REQUIRED_VARS[@]}"; do
  if [[ -z "${!var:-}" ]]; then
    echo "❌  Environment variable '$var' is not set or is empty."
    missing=true
  else
    echo "✅  '$var' is set."
  fi
done

if $missing; then
  echo ""
  echo "One or more required variables are missing.  Aborting deployment."
  exit 1
fi

echo ""
echo "All required variables are present."
