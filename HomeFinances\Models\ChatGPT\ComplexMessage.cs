﻿using System.Text.Json.Serialization;

namespace HomeFinances.Models.ChatGPT
{
    public class ComplexMessage
    {
        [JsonPropertyName("role")]
        public string Role { get; set; }

        [JsonPropertyName("content")]
        public List<Content> Content { get; set; }

        public ComplexMessage()
        {
            Role = "user";
            Content = new List<Content>();
        }
    }
}
